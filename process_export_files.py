#!/usr/bin/env python3
"""
<PERSON><PERSON>t to process Export folder CSV files and organize files based on Source column paths.

This script:
1. Reads CSV files from Export and Export (1) folders
2. Extracts Source column values and transforms paths
3. Creates folder structure based on transformed paths
4. Moves files from Attachments to corresponding folders
"""

import os
import csv
import shutil
import re

from typing import Set, Dict, List, Tuple

def clean_source_path(source_path: str) -> str:
    """
    Clean the source path by removing the partition info and replacing with C:\

    Args:
        source_path: Original source path from CSV

    Returns:
        Cleaned path starting with C:\
    """
    # Pattern to match the partition info part
    pattern = r'^[^-]+ - Partition \d+ \([^)]+\) '

    # Remove the partition info and replace with C:\
    cleaned = re.sub(pattern, r'C:\\', source_path)

    # Ensure we have proper Windows path separators
    cleaned = cleaned.replace('/', '\\')

    return cleaned

def extract_paths_from_csv(csv_file_path: str) -> List[Tuple[str, str]]:
    """
    Extract file paths and source paths from CSV file.
    
    Args:
        csv_file_path: Path to the CSV file
        
    Returns:
        List of tuples (file_path, cleaned_source_path)
    """
    paths = []
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8', newline='') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                if row.get('File') and row.get('Source'):
                    file_path = row['File'].strip()
                    source_path = row['Source'].strip()
                    
                    # Clean the source path
                    cleaned_source = clean_source_path(source_path)
                    
                    paths.append((file_path, cleaned_source))
                    
    except Exception as e:
        print(f"Error reading CSV file {csv_file_path}: {e}")
        
    return paths

def get_unique_folder_paths(all_paths: List[Tuple[str, str]]) -> Set[str]:
    """
    Get unique folder paths from the source paths.
    
    Args:
        all_paths: List of tuples (file_path, source_path)
        
    Returns:
        Set of unique folder paths
    """
    unique_paths = set()
    
    for _, source_path in all_paths:
        # Extract the directory part of the path
        folder_path = os.path.dirname(source_path)
        if folder_path:
            unique_paths.add(folder_path)
    
    return unique_paths

def create_folder_structure(base_dir: str, folder_paths: Set[str]) -> Dict[str, str]:
    """
    Create the folder structure in the specified base directory.

    Args:
        base_dir: Base directory where folders will be created
        folder_paths: Set of folder paths to create

    Returns:
        Dictionary mapping original paths to created paths (for long path handling)
    """
    path_mapping = {}

    for folder_path in folder_paths:
        # Remove C:\ prefix and create relative path
        relative_path = folder_path.replace('C:\\', '').replace('C:', '')

        # Check if path will be too long and simplify if needed
        test_path = os.path.join(base_dir, relative_path)
        if len(test_path) > 200:  # Be more conservative with path length
            # Create a hash-based shortened path for extremely long paths
            import hashlib
            path_hash = hashlib.md5(relative_path.encode()).hexdigest()[:8]

            # Keep only the first directory and create a much shorter path
            path_parts = relative_path.split('\\')
            if len(path_parts) > 1:
                # Keep first directory (Windows) and last directory, add hash in between
                simplified_parts = [path_parts[0], f"LONG_PATH_{path_hash}"]
                if len(path_parts) > 1:
                    simplified_parts.append(path_parts[-1])
            else:
                simplified_parts = [f"LONG_PATH_{path_hash}"]

            relative_path = '\\'.join(simplified_parts)
            print(f"Warning: Path too long, created simplified path")
            print(f"  Original: {folder_path}")
            print(f"  Simplified: {relative_path}")

        full_path = os.path.join(base_dir, relative_path)

        try:
            os.makedirs(full_path, exist_ok=True)
            print(f"Created folder: {full_path}")
            path_mapping[folder_path] = full_path
        except Exception as e:
            print(f"Error creating folder {full_path}: {e}")
            path_mapping[folder_path] = None

    return path_mapping

def move_files_to_folders(export_dir: str, file_mappings: List[Tuple[str, str]], path_mapping: Dict[str, str]) -> None:
    """
    Move files from Attachments to their corresponding folder structure.

    Args:
        export_dir: Export directory path
        file_mappings: List of tuples (file_path, source_path)
        path_mapping: Dictionary mapping original paths to actual created paths
    """
    attachments_dir = os.path.join(export_dir, 'Attachments')

    for file_path, source_path in file_mappings:
        # Extract filename from file_path (remove .\Attachments\ prefix)
        filename = os.path.basename(file_path)
        source_file = os.path.join(attachments_dir, filename)

        # Get the folder path and find the actual created path
        folder_path = os.path.dirname(source_path)

        if folder_path in path_mapping and path_mapping[folder_path]:
            dest_folder = path_mapping[folder_path]
        else:
            # Fallback to original logic if not in mapping
            relative_folder = folder_path.replace('C:\\', '').replace('C:', '')
            dest_folder = os.path.join(export_dir, relative_folder)

        dest_file = os.path.join(dest_folder, filename)

        # Move the file if source exists
        if os.path.exists(source_file):
            try:
                # Ensure destination folder exists
                os.makedirs(dest_folder, exist_ok=True)

                # Move the file
                shutil.move(source_file, dest_file)
                print(f"Moved: {filename} -> {dest_file}")

            except Exception as e:
                print(f"Error moving file {filename}: {e}")
        else:
            print(f"Warning: Source file not found: {source_file}")

def process_export_folder(export_dir: str) -> None:
    """
    Process a single export folder.
    
    Args:
        export_dir: Path to the export directory
    """
    print(f"\nProcessing export folder: {export_dir}")
    
    # Find CSV files in the export directory
    csv_files = [f for f in os.listdir(export_dir) if f.endswith('.csv')]
    
    if not csv_files:
        print(f"No CSV files found in {export_dir}")
        return
    
    all_paths = []
    
    # Process each CSV file
    for csv_file in csv_files:
        csv_path = os.path.join(export_dir, csv_file)
        print(f"Processing CSV: {csv_file}")
        
        paths = extract_paths_from_csv(csv_path)
        all_paths.extend(paths)
        print(f"Found {len(paths)} entries in {csv_file}")
    
    if not all_paths:
        print("No valid paths found in CSV files")
        return
    
    # Get unique folder paths
    unique_folders = get_unique_folder_paths(all_paths)
    print(f"Creating {len(unique_folders)} unique folder paths")
    
    # Create folder structure
    path_mapping = create_folder_structure(export_dir, unique_folders)

    # Move files
    print("Moving files to corresponding folders...")
    move_files_to_folders(export_dir, all_paths, path_mapping)

def create_summary_report(export_folders: List[str]) -> None:
    """Create a summary report of the processing."""
    print("\n" + "=" * 50)
    print("PROCESSING SUMMARY")
    print("=" * 50)

    for folder in export_folders:
        if os.path.exists(folder):
            print(f"\nExport folder: {folder}")

            # Count CSV files
            csv_files = [f for f in os.listdir(folder) if f.endswith('.csv')]
            print(f"  CSV files processed: {len(csv_files)}")

            # Count created folders (excluding Attachments)
            created_folders = []
            for root, _, _ in os.walk(folder):
                if 'Attachments' not in root and root != folder:
                    created_folders.append(root)

            print(f"  Folders created: {len(created_folders)}")

            # Count remaining files in Attachments
            attachments_dir = os.path.join(folder, 'Attachments')
            if os.path.exists(attachments_dir):
                remaining_files = len([f for f in os.listdir(attachments_dir) if os.path.isfile(os.path.join(attachments_dir, f))])
                print(f"  Files remaining in Attachments: {remaining_files}")

def main():
    """Main function to process both Export folders."""
    print("Starting Export Folder Processing Script")
    print("=" * 50)

    # Define export folders
    export_folders = ['Export', 'Export (1)']

    for folder in export_folders:
        if os.path.exists(folder):
            process_export_folder(folder)
        else:
            print(f"Warning: Folder '{folder}' not found")

    # Create summary report
    create_summary_report(export_folders)

    print("\n" + "=" * 50)
    print("Processing completed!")
    print("Files have been organized according to their source paths.")
    print("Check the created folder structure in each Export directory.")

if __name__ == "__main__":
    main()
