#!/usr/bin/env python3
"""
Script to process Export folder CSV files and organize files based on Source column paths.

This script:
1. Reads CSV files from Export and Export (1) folders
2. Extracts Source column values and transforms paths
3. Creates folder structure based on transformed paths
4. Moves files from Attachments to corresponding folders
5. Generates comprehensive Excel logs with detailed tracking
"""

import os
import csv
import shutil
import re
import datetime
import multiprocessing
import threading
import time
import argparse
import sys
from concurrent.futures import <PERSON><PERSON><PERSON>Executor, ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from typing import Set, Dict, List, Tuple
from pathlib import Path

try:
    from openpyxl import Workbook, load_workbook
    from openpyxl.styles import Font, PatternFill, Alignment
    from openpyxl.utils import get_column_letter
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("Warning: openpyxl not installed. Excel logging will be disabled.")
    print("Install with: pip install openpyxl")

@dataclass
class FileProcessingRecord:
    """Data class to track file processing information for logging."""
    carved_filename: str
    origin_filename: str
    replaced_filename: str
    origin_folder_path: str
    replaced_folder_path: str
    error_status: str
    artifacts_type: str
    export_folder: str
    csv_source: str
    timestamp: str

@dataclass
class BatchProcessingResult:
    """Data class to track batch processing results."""
    records: List[FileProcessingRecord]
    processed_count: int
    skipped_count: int
    error_count: int
    processing_time: float

class PerformanceMonitor:
    """Class to monitor and report performance metrics."""

    def __init__(self):
        self.start_time = None
        self.folder_creation_time = 0
        self.file_processing_time = 0
        self.total_files = 0
        self.processed_files = 0
        self.skipped_files = 0
        self.error_files = 0

    def start_timing(self):
        """Start performance timing."""
        self.start_time = time.time()

    def add_folder_time(self, duration: float):
        """Add folder creation time."""
        self.folder_creation_time += duration

    def add_file_processing_time(self, duration: float):
        """Add file processing time."""
        self.file_processing_time += duration

    def update_counts(self, processed: int, skipped: int, errors: int):
        """Update processing counts."""
        self.processed_files += processed
        self.skipped_files += skipped
        self.error_files += errors

    def get_performance_report(self) -> str:
        """Generate performance report."""
        total_time = time.time() - self.start_time if self.start_time else 0
        total_files = self.processed_files + self.skipped_files + self.error_files

        if total_time > 0:
            files_per_second = total_files / total_time
        else:
            files_per_second = 0

        report = f"""
Performance Report:
==================
Total Processing Time: {total_time:.2f} seconds
Folder Creation Time: {self.folder_creation_time:.2f} seconds
File Processing Time: {self.file_processing_time:.2f} seconds

Files Processed: {self.processed_files}
Files Skipped: {self.skipped_files}
Files with Errors: {self.error_files}
Total Files: {total_files}

Processing Rate: {files_per_second:.2f} files/second
"""
        return report

class ExcelLogger:
    """Class to handle Excel logging functionality."""

    def __init__(self):
        self.records: List[FileProcessingRecord] = []
        self.workbook = None
        self.worksheet = None

    def initialize_workbook(self) -> bool:
        """Initialize Excel workbook and worksheet."""
        if not EXCEL_AVAILABLE:
            return False

        try:
            self.workbook = Workbook()
            self.worksheet = self.workbook.active
            self.worksheet.title = "File Processing Log"

            # Set up headers
            headers = [
                "Carved Filename",
                "Origin Filename",
                "Replaced Filename",
                "Origin Folder Path",
                "Replaced Folder Path",
                "Error Status",
                "Artifacts Type",
                "Export Folder",
                "CSV Source",
                "Timestamp"
            ]

            # Write headers with formatting
            for col, header in enumerate(headers, 1):
                cell = self.worksheet.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True, color="FFFFFF")
                cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                cell.alignment = Alignment(horizontal="center", vertical="center")

            # Auto-adjust column widths
            column_widths = [20, 30, 30, 50, 50, 15, 20, 15, 25, 20]
            for col, width in enumerate(column_widths, 1):
                self.worksheet.column_dimensions[get_column_letter(col)].width = width

            return True
        except Exception as e:
            print(f"Error initializing Excel workbook: {e}")
            return False

    def add_record(self, record: FileProcessingRecord):
        """Add a processing record to the log."""
        self.records.append(record)

    def write_to_excel(self, filename: str) -> bool:
        """Write all records to Excel file."""
        if not EXCEL_AVAILABLE or not self.workbook:
            return False

        try:
            # Write data rows
            for row_idx, record in enumerate(self.records, 2):
                self.worksheet.cell(row=row_idx, column=1, value=record.carved_filename)
                self.worksheet.cell(row=row_idx, column=2, value=record.origin_filename)
                self.worksheet.cell(row=row_idx, column=3, value=record.replaced_filename)
                self.worksheet.cell(row=row_idx, column=4, value=record.origin_folder_path)
                self.worksheet.cell(row=row_idx, column=5, value=record.replaced_folder_path)

                # Color-code error status
                error_cell = self.worksheet.cell(row=row_idx, column=6, value=record.error_status)
                if record.error_status == "No error":
                    error_cell.fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
                else:
                    error_cell.fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")

                self.worksheet.cell(row=row_idx, column=7, value=record.artifacts_type)
                self.worksheet.cell(row=row_idx, column=8, value=record.export_folder)
                self.worksheet.cell(row=row_idx, column=9, value=record.csv_source)
                self.worksheet.cell(row=row_idx, column=10, value=record.timestamp)

            # Save the workbook
            self.workbook.save(filename)
            return True
        except Exception as e:
            print(f"Error writing to Excel file: {e}")
            return False

def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename by replacing prohibited characters with underscores.

    Prohibited characters: \\ / : * ? " < > | $ #

    Args:
        filename: Original filename

    Returns:
        Sanitized filename with prohibited characters replaced by _
    """
    prohibited_chars = ['\\', '/', ':', '*', '?', '"', '<', '>', '|', '$', '#']
    sanitized = filename

    for char in prohibited_chars:
        sanitized = sanitized.replace(char, '_')

    return sanitized

def sanitize_path(path: str) -> str:
    """
    Sanitize path by replacing prohibited characters in each path component.

    Args:
        path: Original path

    Returns:
        Sanitized path with prohibited characters replaced by _
    """
    # Split path into components
    if '\\' in path:
        separator = '\\'
        components = path.split('\\')
    else:
        separator = '/'
        components = path.split('/')

    # Sanitize each component except drive letters (C:)
    sanitized_components = []
    for i, component in enumerate(components):
        if i == 0 and component.endswith(':') and len(component) == 2:
            # Keep drive letters as is (C:)
            sanitized_components.append(component)
        else:
            sanitized_components.append(sanitize_filename(component))

    return separator.join(sanitized_components)

def clean_source_path(source_path: str) -> str:
    """
    Clean the source path by removing the partition info and replacing with C:\
    Also sanitizes the path to handle prohibited characters.

    Args:
        source_path: Original source path from CSV

    Returns:
        Cleaned and sanitized path starting with C:\
    """
    # Pattern to match the partition info part
    pattern = r'^[^-]+ - Partition \d+ \([^)]+\) '

    # Remove the partition info and replace with C:\
    cleaned = re.sub(pattern, r'C:\\', source_path)

    # Ensure we have proper Windows path separators
    cleaned = cleaned.replace('/', '\\')

    # Sanitize the path to handle prohibited characters
    cleaned = sanitize_path(cleaned)

    return cleaned

def process_file_batch(batch_data: Tuple[List[Tuple], str, str, Dict[str, str], str]) -> BatchProcessingResult:
    """
    Process a batch of files in a separate process.

    Args:
        batch_data: Tuple containing (file_info_list, export_dir, attachments_dir, path_mapping, csv_source)

    Returns:
        BatchProcessingResult with processing statistics and records
    """
    file_info_list, export_dir, attachments_dir, path_mapping, csv_source = batch_data

    start_time = time.time()
    records = []
    processed_count = 0
    skipped_count = 0
    error_count = 0
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    for carved_filename, origin_filename, cleaned_source_path, original_source_path, artifacts_type in file_info_list:
        # Handle empty carved filename - log as skipped
        if not carved_filename:
            record = FileProcessingRecord(
                carved_filename="",
                origin_filename=origin_filename,
                replaced_filename="",
                origin_folder_path=original_source_path,
                replaced_folder_path="",
                error_status="Skipped - Empty carved filename",
                artifacts_type=artifacts_type,
                export_folder=os.path.basename(export_dir),
                csv_source=csv_source,
                timestamp=timestamp
            )
            records.append(record)
            skipped_count += 1
            continue

        # Initialize record for normal processing
        record = FileProcessingRecord(
            carved_filename=carved_filename,
            origin_filename=origin_filename,
            replaced_filename="",
            origin_folder_path=original_source_path,
            replaced_folder_path="",
            error_status="No error",
            artifacts_type=artifacts_type,
            export_folder=os.path.basename(export_dir),
            csv_source=csv_source,
            timestamp=timestamp
        )

        source_file = os.path.join(attachments_dir, carved_filename)
        folder_path = os.path.dirname(cleaned_source_path)

        # Determine destination folder
        if folder_path in path_mapping and path_mapping[folder_path]:
            dest_folder = path_mapping[folder_path]
        else:
            # Fallback to original logic if not in mapping
            relative_folder = folder_path.replace('C:\\', '').replace('C:', '')
            dest_folder = os.path.join(export_dir, relative_folder)

        record.replaced_folder_path = dest_folder

        # Determine final filename (rename from carved to origin filename)
        final_filename = origin_filename if origin_filename else carved_filename
        record.replaced_filename = final_filename

        dest_file = os.path.join(dest_folder, final_filename)

        # Handle file processing
        if not os.path.exists(source_file):
            record.error_status = f"Source file not found: {carved_filename}"
            error_count += 1
        else:
            try:
                # Ensure destination folder exists
                os.makedirs(dest_folder, exist_ok=True)

                # Handle duplicate filenames in destination
                if os.path.exists(dest_file):
                    base_name, ext = os.path.splitext(final_filename)
                    counter = 1
                    while os.path.exists(dest_file):
                        final_filename = f"{base_name} ({counter}){ext}"
                        dest_file = os.path.join(dest_folder, final_filename)
                        counter += 1
                    record.replaced_filename = final_filename
                    record.error_status = f"Renamed due to duplicate: {final_filename}"

                # Move and rename the file
                shutil.move(source_file, dest_file)
                processed_count += 1

            except Exception as e:
                record.error_status = f"Error moving file: {str(e)}"
                error_count += 1

        records.append(record)

    processing_time = time.time() - start_time

    return BatchProcessingResult(
        records=records,
        processed_count=processed_count,
        skipped_count=skipped_count,
        error_count=error_count,
        processing_time=processing_time
    )

def create_folders_batch(folder_batch: List[str], base_dir: str) -> Dict[str, str]:
    """
    Create a batch of folders efficiently.

    Args:
        folder_batch: List of folder paths to create
        base_dir: Base directory for folder creation

    Returns:
        Dictionary mapping original paths to created paths
    """
    path_mapping = {}

    for folder_path in folder_batch:
        # Remove C:\ prefix and create relative path
        relative_path = folder_path.replace('C:\\', '').replace('C:', '')

        # Check if path will be too long and simplify if needed
        test_path = os.path.join(base_dir, relative_path)
        if len(test_path) > 200:  # Be more conservative with path length
            # Create a hash-based shortened path for extremely long paths
            import hashlib
            path_hash = hashlib.md5(relative_path.encode()).hexdigest()[:8]

            # Keep only the first directory and create a much shorter path
            path_parts = relative_path.split('\\')
            if len(path_parts) > 1:
                # Keep first directory (Windows) and last directory, add hash in between
                simplified_parts = [path_parts[0], f"LONG_PATH_{path_hash}"]
                if len(path_parts) > 1:
                    simplified_parts.append(path_parts[-1])
            else:
                simplified_parts = [f"LONG_PATH_{path_hash}"]

            relative_path = '\\'.join(simplified_parts)

        full_path = os.path.join(base_dir, relative_path)

        try:
            os.makedirs(full_path, exist_ok=True)
            path_mapping[folder_path] = full_path
        except Exception as e:
            print(f"Error creating folder {full_path}: {e}")
            path_mapping[folder_path] = None

    return path_mapping

def read_excel_file(excel_file_path: str) -> List[Dict[str, str]]:
    """
    Read Excel file and return list of dictionaries.

    Args:
        excel_file_path: Path to the Excel file

    Returns:
        List of dictionaries representing rows
    """
    if not EXCEL_AVAILABLE:
        raise ImportError("openpyxl is required to read Excel files. Install with: pip install openpyxl")

    try:
        workbook = load_workbook(excel_file_path, read_only=True)
        worksheet = workbook.active

        # Get headers from first row
        headers = []
        for cell in worksheet[1]:
            headers.append(cell.value if cell.value else "")

        # Read data rows
        data = []
        for row in worksheet.iter_rows(min_row=2, values_only=True):
            row_dict = {}
            for i, value in enumerate(row):
                if i < len(headers):
                    row_dict[headers[i]] = str(value) if value is not None else ""
            data.append(row_dict)

        workbook.close()
        return data

    except Exception as e:
        print(f"Error reading Excel file {excel_file_path}: {e}")
        return []

def extract_file_info_from_datasheet(datasheet_path: str) -> List[Tuple[str, str, str, str, str]]:
    """
    Extract file information from CSV or Excel file using correct column headers.

    Expected columns:
    - "Carved Filename": The carved filename (e.g., 0104731_Carved.png)
    - "Filename": The original filename (e.g., Testing_123.png)
    - "Source": The original source path
    - "Artifacts type": The type of artifact (optional)

    Args:
        datasheet_path: Path to the CSV or Excel file

    Returns:
        List of tuples (carved_filename, origin_filename, cleaned_source_path, original_source_path, artifacts_type)
        Note: Empty carved filenames are handled separately and logged as skipped entries
    """
    file_info = []
    file_extension = os.path.splitext(datasheet_path)[1].lower()

    try:
        # Determine file type and read data
        if file_extension in ['.xlsx', '.xls']:
            print(f"Reading Excel file: {datasheet_path}")
            data_rows = read_excel_file(datasheet_path)
        elif file_extension == '.csv':
            print(f"Reading CSV file: {datasheet_path}")
            data_rows = []
            with open(datasheet_path, 'r', encoding='utf-8', newline='') as file:
                reader = csv.DictReader(file)
                data_rows = list(reader)
        else:
            print(f"Error: Unsupported file format: {file_extension}")
            print("Supported formats: .csv, .xlsx, .xls")
            return file_info

        if not data_rows:
            print(f"No data found in file: {datasheet_path}")
            return file_info

        # Check if required columns exist
        required_columns = ['Carved Filename', 'Filename', 'Source']
        available_columns = list(data_rows[0].keys()) if data_rows else []

        if not all(col in available_columns for col in required_columns):
            print(f"Warning: File {datasheet_path} missing required columns.")
            print(f"Required: {required_columns}")
            print(f"Found: {available_columns}")
            return file_info

        print(f"Found {len(data_rows)} rows in datasheet")

        for row in data_rows:
            carved_filename = row.get('Carved Filename', '').strip()
            origin_filename = row.get('Filename', '').strip()
            source_path = row.get('Source', '').strip()
            artifacts_type = row.get('Artifacts type', '').strip()

            # Handle empty carved filename - skip processing but log the event
            if not carved_filename:
                print(f"Warning: Skipping row with empty carved filename - Origin: '{origin_filename}', Source: '{source_path}'")
                # Create a special entry for logging this skip event
                skip_entry = ("", origin_filename, "", source_path, artifacts_type)
                file_info.append(skip_entry)
                continue

            if origin_filename and source_path:
                # Clean the source path and sanitize it
                cleaned_source = clean_source_path(source_path)

                # Sanitize the origin filename
                sanitized_origin_filename = sanitize_filename(origin_filename)

                file_info.append((carved_filename, sanitized_origin_filename, cleaned_source, source_path, artifacts_type))
            else:
                print(f"Warning: Skipping row with missing data - Carved: '{carved_filename}', Origin: '{origin_filename}', Source: '{source_path}'")

    except Exception as e:
        print(f"Error reading file {datasheet_path}: {e}")

    return file_info

# Legacy function for backward compatibility
def extract_file_info_from_csv(csv_file_path: str) -> List[Tuple[str, str, str, str, str]]:
    """Legacy function - use extract_file_info_from_datasheet instead."""
    return extract_file_info_from_datasheet(csv_file_path)

def extract_paths_from_csv(csv_file_path: str) -> List[Tuple[str, str]]:
    """
    Extract file paths and source paths from CSV file (legacy function for compatibility).

    Args:
        csv_file_path: Path to the CSV file

    Returns:
        List of tuples (carved_filename, cleaned_source_path)
    """
    file_info = extract_file_info_from_csv(csv_file_path)
    # Convert to legacy format for existing code compatibility, skip empty carved filenames
    return [(info[0], info[2]) for info in file_info if info[0]]  # Only include entries with carved filename

def get_unique_folder_paths(all_paths: List[Tuple[str, str]]) -> Set[str]:
    """
    Get unique folder paths from the source paths.
    
    Args:
        all_paths: List of tuples (file_path, source_path)
        
    Returns:
        Set of unique folder paths
    """
    unique_paths = set()
    
    for _, source_path in all_paths:
        # Extract the directory part of the path
        folder_path = os.path.dirname(source_path)
        if folder_path:
            unique_paths.add(folder_path)
    
    return unique_paths

def create_folder_structure(base_dir: str, folder_paths: Set[str]) -> Dict[str, str]:
    """
    Create the folder structure in the specified base directory.

    Args:
        base_dir: Base directory where folders will be created
        folder_paths: Set of folder paths to create

    Returns:
        Dictionary mapping original paths to created paths (for long path handling)
    """
    path_mapping = {}

    for folder_path in folder_paths:
        # Remove C:\ prefix and create relative path
        relative_path = folder_path.replace('C:\\', '').replace('C:', '')

        # Check if path will be too long and simplify if needed
        test_path = os.path.join(base_dir, relative_path)
        if len(test_path) > 200:  # Be more conservative with path length
            # Create a hash-based shortened path for extremely long paths
            import hashlib
            path_hash = hashlib.md5(relative_path.encode()).hexdigest()[:8]

            # Keep only the first directory and create a much shorter path
            path_parts = relative_path.split('\\')
            if len(path_parts) > 1:
                # Keep first directory (Windows) and last directory, add hash in between
                simplified_parts = [path_parts[0], f"LONG_PATH_{path_hash}"]
                if len(path_parts) > 1:
                    simplified_parts.append(path_parts[-1])
            else:
                simplified_parts = [f"LONG_PATH_{path_hash}"]

            relative_path = '\\'.join(simplified_parts)
            print(f"Warning: Path too long, created simplified path")
            print(f"  Original: {folder_path}")
            print(f"  Simplified: {relative_path}")

        full_path = os.path.join(base_dir, relative_path)

        try:
            os.makedirs(full_path, exist_ok=True)
            print(f"Created folder: {full_path}")
            path_mapping[folder_path] = full_path
        except Exception as e:
            print(f"Error creating folder {full_path}: {e}")
            path_mapping[folder_path] = None

    return path_mapping

def move_files_to_folders_with_logging(export_dir: str, file_info_list: List[Tuple[str, str, str, str, str]],
                                     path_mapping: Dict[str, str], logger: ExcelLogger,
                                     csv_source: str) -> None:
    """
    Move files from Attachments to their corresponding folder structure with comprehensive logging.

    Args:
        export_dir: Export directory path
        file_info_list: List of tuples (carved_filename, origin_filename, cleaned_source_path, original_source_path, artifacts_type)
        path_mapping: Dictionary mapping original paths to actual created paths
        logger: ExcelLogger instance for logging
        csv_source: Name of the CSV file being processed
    """
    attachments_dir = os.path.join(export_dir, 'Attachments')
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    for carved_filename, origin_filename, cleaned_source_path, original_source_path, artifacts_type in file_info_list:
        # Handle empty carved filename - log as skipped
        if not carved_filename:
            record = FileProcessingRecord(
                carved_filename="",
                origin_filename=origin_filename,
                replaced_filename="",
                origin_folder_path=original_source_path,
                replaced_folder_path="",
                error_status="Skipped - Empty carved filename",
                artifacts_type=artifacts_type,
                export_folder=os.path.basename(export_dir),
                csv_source=csv_source,
                timestamp=timestamp
            )
            logger.add_record(record)
            continue

        # Initialize record for normal processing
        record = FileProcessingRecord(
            carved_filename=carved_filename,
            origin_filename=origin_filename,
            replaced_filename="",
            origin_folder_path=original_source_path,  # Use original source path for logging
            replaced_folder_path="",
            error_status="No error",
            artifacts_type=artifacts_type,
            export_folder=os.path.basename(export_dir),
            csv_source=csv_source,
            timestamp=timestamp
        )

        source_file = os.path.join(attachments_dir, carved_filename)
        folder_path = os.path.dirname(cleaned_source_path)

        # Determine destination folder
        if folder_path in path_mapping and path_mapping[folder_path]:
            dest_folder = path_mapping[folder_path]
        else:
            # Fallback to original logic if not in mapping
            relative_folder = folder_path.replace('C:\\', '').replace('C:', '')
            dest_folder = os.path.join(export_dir, relative_folder)

        record.replaced_folder_path = dest_folder

        # Determine final filename (rename from carved to origin filename)
        final_filename = origin_filename if origin_filename else carved_filename
        record.replaced_filename = final_filename

        dest_file = os.path.join(dest_folder, final_filename)

        # Handle file processing
        if not os.path.exists(source_file):
            record.error_status = f"Source file not found: {carved_filename}"
            print(f"Warning: Source file not found: {source_file}")
        else:
            try:
                # Ensure destination folder exists
                os.makedirs(dest_folder, exist_ok=True)

                # Handle duplicate filenames in destination
                if os.path.exists(dest_file):
                    base_name, ext = os.path.splitext(final_filename)
                    counter = 1
                    while os.path.exists(dest_file):
                        final_filename = f"{base_name} ({counter}){ext}"
                        dest_file = os.path.join(dest_folder, final_filename)
                        counter += 1
                    record.replaced_filename = final_filename
                    record.error_status = f"Renamed due to duplicate: {final_filename}"

                # Move and rename the file
                shutil.move(source_file, dest_file)
                print(f"Moved and renamed: {carved_filename} -> {final_filename} in {dest_folder}")

            except Exception as e:
                record.error_status = f"Error moving file: {str(e)}"
                print(f"Error moving file {carved_filename}: {e}")

        # Add record to logger
        logger.add_record(record)

def move_files_to_folders(export_dir: str, file_mappings: List[Tuple[str, str]], path_mapping: Dict[str, str]) -> None:
    """
    Legacy function for backward compatibility.
    Move files from Attachments to their corresponding folder structure.

    Args:
        export_dir: Export directory path
        file_mappings: List of tuples (file_path, source_path)
        path_mapping: Dictionary mapping original paths to actual created paths
    """
    attachments_dir = os.path.join(export_dir, 'Attachments')

    for file_path, source_path in file_mappings:
        # Extract filename from file_path (remove .\Attachments\ prefix)
        filename = os.path.basename(file_path)
        source_file = os.path.join(attachments_dir, filename)

        # Get the folder path and find the actual created path
        folder_path = os.path.dirname(source_path)

        if folder_path in path_mapping and path_mapping[folder_path]:
            dest_folder = path_mapping[folder_path]
        else:
            # Fallback to original logic if not in mapping
            relative_folder = folder_path.replace('C:\\', '').replace('C:', '')
            dest_folder = os.path.join(export_dir, relative_folder)

        dest_file = os.path.join(dest_folder, filename)

        # Move the file if source exists
        if os.path.exists(source_file):
            try:
                # Ensure destination folder exists
                os.makedirs(dest_folder, exist_ok=True)

                # Move the file
                shutil.move(source_file, dest_file)
                print(f"Moved: {filename} -> {dest_file}")

            except Exception as e:
                print(f"Error moving file {filename}: {e}")
        else:
            print(f"Warning: Source file not found: {source_file}")

def move_files_to_folders_with_logging_custom(output_dir: str, attachments_dir: str, file_info_list: List[Tuple[str, str, str, str, str]],
                                            path_mapping: Dict[str, str], logger: ExcelLogger,
                                            datasheet_name: str) -> None:
    """
    Move files from custom attachments directory to organized structure with comprehensive logging.

    Args:
        output_dir: Output directory path
        attachments_dir: Custom attachments directory path
        file_info_list: List of tuples (carved_filename, origin_filename, cleaned_source_path, original_source_path, artifacts_type)
        path_mapping: Dictionary mapping original paths to actual created paths
        logger: ExcelLogger instance for logging
        datasheet_name: Name of the datasheet file being processed
    """
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    for carved_filename, origin_filename, cleaned_source_path, original_source_path, artifacts_type in file_info_list:
        # Handle empty carved filename - log as skipped
        if not carved_filename:
            record = FileProcessingRecord(
                carved_filename="",
                origin_filename=origin_filename,
                replaced_filename="",
                origin_folder_path=original_source_path,
                replaced_folder_path="",
                error_status="Skipped - Empty carved filename",
                artifacts_type=artifacts_type,
                export_folder=os.path.basename(output_dir),
                csv_source=datasheet_name,
                timestamp=timestamp
            )
            logger.add_record(record)
            continue

        # Initialize record for normal processing
        record = FileProcessingRecord(
            carved_filename=carved_filename,
            origin_filename=origin_filename,
            replaced_filename="",
            origin_folder_path=original_source_path,
            replaced_folder_path="",
            error_status="No error",
            artifacts_type=artifacts_type,
            export_folder=os.path.basename(output_dir),
            csv_source=datasheet_name,
            timestamp=timestamp
        )

        source_file = os.path.join(attachments_dir, carved_filename)
        folder_path = os.path.dirname(cleaned_source_path)

        # Determine destination folder
        if folder_path in path_mapping and path_mapping[folder_path]:
            dest_folder = path_mapping[folder_path]
        else:
            # Fallback to original logic if not in mapping
            relative_folder = folder_path.replace('C:\\', '').replace('C:', '')
            dest_folder = os.path.join(output_dir, relative_folder)

        record.replaced_folder_path = dest_folder

        # Determine final filename (rename from carved to origin filename)
        final_filename = origin_filename if origin_filename else carved_filename
        record.replaced_filename = final_filename

        dest_file = os.path.join(dest_folder, final_filename)

        # Handle file processing
        if not os.path.exists(source_file):
            record.error_status = f"Source file not found: {carved_filename}"
            print(f"⚠️  Warning: Source file not found: {source_file}")
        else:
            try:
                # Ensure destination folder exists
                os.makedirs(dest_folder, exist_ok=True)

                # Handle duplicate filenames in destination
                if os.path.exists(dest_file):
                    base_name, ext = os.path.splitext(final_filename)
                    counter = 1
                    while os.path.exists(dest_file):
                        final_filename = f"{base_name} ({counter}){ext}"
                        dest_file = os.path.join(dest_folder, final_filename)
                        counter += 1
                    record.replaced_filename = final_filename
                    record.error_status = f"Renamed due to duplicate: {final_filename}"

                # Move and rename the file
                shutil.move(source_file, dest_file)
                print(f"✅ Moved and renamed: {carved_filename} -> {final_filename} in {dest_folder}")

            except Exception as e:
                record.error_status = f"Error moving file: {str(e)}"
                print(f"❌ Error moving file {carved_filename}: {e}")

        # Add record to logger
        logger.add_record(record)

def process_datasheet_with_logging(datasheet_path: str, attachments_dir: str, output_dir: str, logger: ExcelLogger) -> None:
    """
    Process a datasheet (CSV or Excel) with comprehensive logging.

    Args:
        datasheet_path: Path to the CSV or Excel datasheet
        attachments_dir: Path to the attachments directory containing carved files
        output_dir: Path to the output directory for organized files
        logger: ExcelLogger instance for logging
    """
    print(f"\n📄 Processing datasheet: {datasheet_path}")
    print(f"📁 Attachments directory: {attachments_dir}")
    print(f"📂 Output directory: {output_dir}")

    # Validate input paths
    if not os.path.exists(datasheet_path):
        print(f"❌ Error: Datasheet file not found: {datasheet_path}")
        return

    if not os.path.exists(attachments_dir):
        print(f"❌ Error: Attachments directory not found: {attachments_dir}")
        return

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Extract file information from datasheet
    file_info = extract_file_info_from_datasheet(datasheet_path)

    if not file_info:
        print("❌ No valid file information found in datasheet")
        return

    # Convert to legacy format for folder creation (only entries with carved filenames)
    paths = [(info[0], info[2]) for info in file_info if info[0]]  # Skip empty carved filenames

    print(f"📊 Found {len(file_info)} entries ({len(paths)} with carved files)")

    if not paths:
        print("⚠️  No entries with carved filenames found")
        return

    # Get unique folder paths (only from entries with carved filenames)
    unique_folders = get_unique_folder_paths(paths)
    print(f"📁 Creating {len(unique_folders)} unique folder paths...")

    # Create folder structure in output directory
    path_mapping = create_folder_structure(output_dir, unique_folders)

    # Move files with logging
    print("🔄 Moving files to corresponding folders...")
    datasheet_name = os.path.basename(datasheet_path)
    move_files_to_folders_with_logging_custom(output_dir, attachments_dir, file_info, path_mapping, logger, datasheet_name)

def process_export_folder_with_logging(export_dir: str, logger: ExcelLogger) -> None:
    """
    Process a single export folder with comprehensive logging.

    Args:
        export_dir: Path to the export directory
        logger: ExcelLogger instance for logging
    """
    print(f"\nProcessing export folder: {export_dir}")

    # Find CSV files in the export directory
    csv_files = [f for f in os.listdir(export_dir) if f.endswith('.csv')]

    if not csv_files:
        print(f"No CSV files found in {export_dir}")
        return

    all_file_info = []
    all_paths = []

    # Process each CSV file
    for csv_file in csv_files:
        csv_path = os.path.join(export_dir, csv_file)
        print(f"Processing CSV: {csv_file}")

        file_info = extract_file_info_from_csv(csv_path)
        all_file_info.extend([(info[0], info[1], info[2], info[3], info[4], csv_file) for info in file_info])

        # Convert to legacy format for folder creation (only entries with carved filenames)
        paths = [(info[0], info[2]) for info in file_info if info[0]]  # Skip empty carved filenames
        all_paths.extend(paths)
        print(f"Found {len(file_info)} entries in {csv_file} ({len([info for info in file_info if info[0]])} with carved files)")

    if not all_file_info:
        print("No valid file information found in CSV files")
        return

    # Get unique folder paths (only from entries with carved filenames)
    unique_folders = get_unique_folder_paths(all_paths)
    print(f"Creating {len(unique_folders)} unique folder paths")

    # Create folder structure
    path_mapping = create_folder_structure(export_dir, unique_folders)

    # Process files by CSV source for better logging
    csv_groups = {}
    for carved, origin, cleaned_source, original_source, artifacts_type, csv_file in all_file_info:
        if csv_file not in csv_groups:
            csv_groups[csv_file] = []
        csv_groups[csv_file].append((carved, origin, cleaned_source, original_source, artifacts_type))

    # Move files with logging
    print("Moving files to corresponding folders...")
    for csv_file, file_list in csv_groups.items():
        move_files_to_folders_with_logging(export_dir, file_list, path_mapping, logger, csv_file)

def process_export_folder_high_performance(export_dir: str, logger: ExcelLogger,
                                         max_workers: int = None, batch_size: int = 100) -> None:
    """
    High-performance version using multiprocessing for large file operations.

    Args:
        export_dir: Path to the export directory
        logger: ExcelLogger instance for logging
        max_workers: Maximum number of worker processes (default: CPU count)
        batch_size: Number of files to process per batch (default: 100)
    """
    if max_workers is None:
        max_workers = os.cpu_count()

    print(f"\n🚀 High-Performance Processing: {export_dir}")
    print(f"   Using {max_workers} CPU cores, batch size: {batch_size}")

    # Initialize performance monitor
    perf_monitor = PerformanceMonitor()
    perf_monitor.start_timing()

    # Find CSV files in the export directory
    csv_files = [f for f in os.listdir(export_dir) if f.endswith('.csv')]

    if not csv_files:
        print(f"No CSV files found in {export_dir}")
        return

    all_file_info = []
    all_paths = []

    # Process each CSV file
    for csv_file in csv_files:
        csv_path = os.path.join(export_dir, csv_file)
        print(f"📄 Processing CSV: {csv_file}")

        file_info = extract_file_info_from_csv(csv_path)
        all_file_info.extend([(info[0], info[1], info[2], info[3], info[4], csv_file) for info in file_info])

        # Convert to legacy format for folder creation (only entries with carved filenames)
        paths = [(info[0], info[2]) for info in file_info if info[0]]  # Skip empty carved filenames
        all_paths.extend(paths)
        print(f"   Found {len(file_info)} entries ({len([info for info in file_info if info[0]])} with carved files)")

    if not all_file_info:
        print("No valid file information found in CSV files")
        return

    # Get unique folder paths (only from entries with carved filenames)
    unique_folders = get_unique_folder_paths(all_paths)
    print(f"📁 Creating {len(unique_folders)} unique folder paths...")

    # High-performance folder creation using threading
    folder_start_time = time.time()
    folder_batches = [list(unique_folders)[i:i + batch_size]
                     for i in range(0, len(unique_folders), batch_size)]

    path_mapping = {}
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        folder_futures = [executor.submit(create_folders_batch, batch, export_dir)
                         for batch in folder_batches]

        for future in as_completed(folder_futures):
            batch_mapping = future.result()
            path_mapping.update(batch_mapping)

    folder_time = time.time() - folder_start_time
    perf_monitor.add_folder_time(folder_time)
    print(f"   ✅ Folder creation completed in {folder_time:.2f} seconds")

    # Prepare file processing batches by CSV source
    csv_groups = {}
    for carved, origin, cleaned_source, original_source, artifacts_type, csv_file in all_file_info:
        if csv_file not in csv_groups:
            csv_groups[csv_file] = []
        csv_groups[csv_file].append((carved, origin, cleaned_source, original_source, artifacts_type))

    # High-performance file processing using multiprocessing
    print(f"🔄 Processing files using {max_workers} CPU cores...")
    file_start_time = time.time()

    attachments_dir = os.path.join(export_dir, 'Attachments')

    # Create batches for multiprocessing
    all_batches = []
    for csv_file, file_list in csv_groups.items():
        # Split file list into smaller batches
        for i in range(0, len(file_list), batch_size):
            batch = file_list[i:i + batch_size]
            batch_data = (batch, export_dir, attachments_dir, path_mapping, csv_file)
            all_batches.append(batch_data)

    print(f"   Processing {len(all_batches)} batches...")

    # Process batches using multiprocessing
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        batch_futures = [executor.submit(process_file_batch, batch_data)
                        for batch_data in all_batches]

        completed_batches = 0
        for future in as_completed(batch_futures):
            batch_result = future.result()

            # Add records to logger
            for record in batch_result.records:
                logger.add_record(record)

            # Update performance metrics
            perf_monitor.update_counts(
                batch_result.processed_count,
                batch_result.skipped_count,
                batch_result.error_count
            )
            perf_monitor.add_file_processing_time(batch_result.processing_time)

            completed_batches += 1
            if completed_batches % 10 == 0:  # Progress update every 10 batches
                print(f"   Progress: {completed_batches}/{len(all_batches)} batches completed")

    file_time = time.time() - file_start_time
    print(f"   ✅ File processing completed in {file_time:.2f} seconds")

    # Print performance report
    print(perf_monitor.get_performance_report())

def process_export_folder(export_dir: str) -> None:
    """
    Legacy function for backward compatibility.
    Process a single export folder.

    Args:
        export_dir: Path to the export directory
    """
    print(f"\nProcessing export folder: {export_dir}")

    # Find CSV files in the export directory
    csv_files = [f for f in os.listdir(export_dir) if f.endswith('.csv')]

    if not csv_files:
        print(f"No CSV files found in {export_dir}")
        return

    all_paths = []

    # Process each CSV file
    for csv_file in csv_files:
        csv_path = os.path.join(export_dir, csv_file)
        print(f"Processing CSV: {csv_file}")

        paths = extract_paths_from_csv(csv_path)
        all_paths.extend(paths)
        print(f"Found {len(paths)} entries in {csv_file}")

    if not all_paths:
        print("No valid paths found in CSV files")
        return

    # Get unique folder paths
    unique_folders = get_unique_folder_paths(all_paths)
    print(f"Creating {len(unique_folders)} unique folder paths")

    # Create folder structure
    path_mapping = create_folder_structure(export_dir, unique_folders)

    # Move files
    print("Moving files to corresponding folders...")
    move_files_to_folders(export_dir, all_paths, path_mapping)

def create_summary_report(export_folders: List[str]) -> None:
    """Create a summary report of the processing."""
    print("\n" + "=" * 50)
    print("PROCESSING SUMMARY")
    print("=" * 50)

    for folder in export_folders:
        if os.path.exists(folder):
            print(f"\nExport folder: {folder}")

            # Count CSV files
            csv_files = [f for f in os.listdir(folder) if f.endswith('.csv')]
            print(f"  CSV files processed: {len(csv_files)}")

            # Count created folders (excluding Attachments)
            created_folders = []
            for root, _, _ in os.walk(folder):
                if 'Attachments' not in root and root != folder:
                    created_folders.append(root)

            print(f"  Folders created: {len(created_folders)}")

            # Count remaining files in Attachments
            attachments_dir = os.path.join(folder, 'Attachments')
            if os.path.exists(attachments_dir):
                remaining_files = len([f for f in os.listdir(attachments_dir) if os.path.isfile(os.path.join(attachments_dir, f))])
                print(f"  Files remaining in Attachments: {remaining_files}")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Export File Processing Script - Organize carved files based on datasheet information",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process with CSV datasheet and custom attachments folder
  python process_export_files.py -s data.csv -a /path/to/attachments

  # Process with Excel datasheet
  python process_export_files.py -s Compiled_test.xlsx -a D:\\Project\\Attachments

  # Use standard mode instead of high-performance
  python process_export_files.py -s data.csv -a /path/to/attachments --standard

  # Legacy mode (process Export folders in current directory)
  python process_export_files.py --legacy
        """
    )

    parser.add_argument('-s', '--source',
                       help='Path to the centralized CSV or Excel datasheet')
    parser.add_argument('-a', '--attachments',
                       help='Path to the attachments folder containing carved files')
    parser.add_argument('-o', '--output',
                       help='Output directory (default: current directory + "Organized_Files")')
    parser.add_argument('--standard', action='store_true',
                       help='Use standard single-threaded mode instead of high-performance mode')
    parser.add_argument('--legacy', action='store_true',
                       help='Use legacy mode (process Export folders in current directory)')
    parser.add_argument('--max-workers', type=int,
                       help='Maximum number of worker processes (default: CPU count)')
    parser.add_argument('--batch-size', type=int, default=100,
                       help='Number of files to process per batch (default: 100)')

    return parser.parse_args()

def main_with_parameters(datasheet_path: str, attachments_dir: str, output_dir: str = None,
                        high_performance: bool = True, max_workers: int = None, batch_size: int = 100):
    """
    Main function to process files with custom parameters.

    Args:
        datasheet_path: Path to the CSV or Excel datasheet
        attachments_dir: Path to the attachments directory
        output_dir: Output directory (default: current directory + "Organized_Files")
        high_performance: Use high-performance multiprocessing mode (default: True)
        max_workers: Maximum number of worker processes (default: CPU count)
        batch_size: Number of files to process per batch (default: 100)
    """
    # Set default output directory if not provided
    if output_dir is None:
        output_dir = os.path.join(os.getcwd(), "Organized_Files")

    # Convert to absolute paths
    datasheet_path = os.path.abspath(datasheet_path)
    attachments_dir = os.path.abspath(attachments_dir)
    output_dir = os.path.abspath(output_dir)

    if high_performance:
        print("🚀 Starting HIGH-PERFORMANCE Export Folder Processing Script")
        print("=" * 70)
        print(f"💻 System Info: {os.cpu_count()} CPU cores detected")
        if max_workers is None:
            max_workers = os.cpu_count()
        print(f"⚙️  Configuration: {max_workers} workers, batch size: {batch_size}")
    else:
        print("Starting Export Folder Processing Script with Excel Logging")
        print("=" * 60)

    print(f"📄 Datasheet: {datasheet_path}")
    print(f"📁 Attachments: {attachments_dir}")
    print(f"📂 Output: {output_dir}")

    # Initialize Excel logger
    logger = ExcelLogger()
    excel_initialized = logger.initialize_workbook()

    if excel_initialized:
        print("✓ Excel logging initialized successfully")
    else:
        print("⚠ Excel logging not available - continuing without logging")

    # Overall performance tracking
    overall_start_time = time.time()

    # Process the datasheet
    if excel_initialized:
        process_datasheet_with_logging(datasheet_path, attachments_dir, output_dir, logger)
    else:
        print("❌ Excel logging required for this mode")
        return

    # Save Excel log
    if excel_initialized and logger.records:
        print("\n💾 Saving Excel log...")
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        datasheet_name = os.path.splitext(os.path.basename(datasheet_path))[0]
        log_filename = f"File_Processing_Log_{datasheet_name}_{timestamp}.xlsx"

        if logger.write_to_excel(log_filename):
            print(f"✅ Excel log saved successfully: {log_filename}")
            print(f"  Total records logged: {len(logger.records)}")
        else:
            print("⚠ Failed to save Excel log")
    elif excel_initialized:
        print("\n⚠ No records to log")

    # Overall performance summary
    total_time = time.time() - overall_start_time
    if high_performance:
        print("\n" + "=" * 70)
        print("🎯 HIGH-PERFORMANCE PROCESSING COMPLETED!")
        print(f"⏱️  Total execution time: {total_time:.2f} seconds")
        print(f"📊 Total records processed: {len(logger.records) if excel_initialized else 'N/A'}")
    else:
        print("\n" + "=" * 60)
        print("Processing completed!")

    print("Files have been organized according to their source paths.")
    print(f"Check the organized files in: {output_dir}")
    if excel_initialized:
        print("Check the Excel log file for detailed processing information.")

def main(high_performance: bool = True, max_workers: int = None, batch_size: int = 100):
    """
    Legacy main function to process both Export folders with Excel logging.

    Args:
        high_performance: Use high-performance multiprocessing mode (default: True)
        max_workers: Maximum number of worker processes (default: CPU count)
        batch_size: Number of files to process per batch (default: 100)
    """
    if high_performance:
        print("🚀 Starting HIGH-PERFORMANCE Export Folder Processing Script (Legacy Mode)")
        print("=" * 70)
        print(f"💻 System Info: {os.cpu_count()} CPU cores detected")
        if max_workers is None:
            max_workers = os.cpu_count()
        print(f"⚙️  Configuration: {max_workers} workers, batch size: {batch_size}")
    else:
        print("Starting Export Folder Processing Script with Excel Logging (Legacy Mode)")
        print("=" * 60)

    # Initialize Excel logger
    logger = ExcelLogger()
    excel_initialized = logger.initialize_workbook()

    if excel_initialized:
        print("✓ Excel logging initialized successfully")
    else:
        print("⚠ Excel logging not available - continuing without logging")

    # Define export folders
    export_folders = ['Export', 'Export (1)']

    # Overall performance tracking
    overall_start_time = time.time()

    for folder in export_folders:
        if os.path.exists(folder):
            if excel_initialized:
                if high_performance:
                    process_export_folder_high_performance(folder, logger, max_workers, batch_size)
                else:
                    process_export_folder_with_logging(folder, logger)
            else:
                process_export_folder(folder)
        else:
            print(f"Warning: Folder '{folder}' not found")

    # Create summary report
    create_summary_report(export_folders)

    # Save Excel log
    if excel_initialized and logger.records:
        print("\n💾 Saving Excel log...")
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"File_Processing_Log_{timestamp}.xlsx"

        if logger.write_to_excel(log_filename):
            print(f"✓ Excel log saved successfully: {log_filename}")
            print(f"  Total records logged: {len(logger.records)}")
        else:
            print("⚠ Failed to save Excel log")
    elif excel_initialized:
        print("\n⚠ No records to log")

    # Overall performance summary
    total_time = time.time() - overall_start_time
    if high_performance:
        print("\n" + "=" * 70)
        print("🎯 HIGH-PERFORMANCE PROCESSING COMPLETED!")
        print(f"⏱️  Total execution time: {total_time:.2f} seconds")
        print(f"📊 Total records processed: {len(logger.records) if excel_initialized else 'N/A'}")
    else:
        print("\n" + "=" * 60)
        print("Processing completed!")

    print("Files have been organized according to their source paths.")
    print("Check the created folder structure in each Export directory.")
    if excel_initialized:
        print("Check the Excel log file for detailed processing information.")

def main_standard():
    """Standard processing mode for backward compatibility."""
    main(high_performance=False)

def main_high_performance(max_workers: int = None, batch_size: int = 100):
    """High-performance processing mode with multiprocessing."""
    main(high_performance=True, max_workers=max_workers, batch_size=batch_size)

if __name__ == "__main__":
    args = parse_arguments()

    # Handle legacy mode
    if args.legacy:
        print("� Running in LEGACY mode (processing Export folders)")
        if args.standard:
            main_standard()
        else:
            main_high_performance(args.max_workers, args.batch_size)
        sys.exit(0)

    # Check if required parameters are provided
    if not args.source or not args.attachments:
        print("❌ Error: Both --source (-s) and --attachments (-a) parameters are required")
        print("\nUsage examples:")
        print("  python process_export_files.py -s Compiled_test.xlsx -a D:\\Project\\Attachments")
        print("  python process_export_files.py -s data.csv -a /path/to/attachments")
        print("  python process_export_files.py --help  # For full help")
        sys.exit(1)

    # Validate file extensions
    source_ext = os.path.splitext(args.source)[1].lower()
    if source_ext not in ['.csv', '.xlsx', '.xls']:
        print(f"❌ Error: Unsupported source file format: {source_ext}")
        print("Supported formats: .csv, .xlsx, .xls")
        sys.exit(1)

    # Set performance mode
    high_performance = not args.standard

    # Display configuration
    print("🚀 Export File Processing Script - Parameter Mode")
    print("=" * 60)
    print(f"📄 Source datasheet: {args.source}")
    print(f"📁 Attachments folder: {args.attachments}")
    print(f"📂 Output directory: {args.output or 'Organized_Files (default)'}")
    print(f"⚙️  Performance mode: {'High-Performance' if high_performance else 'Standard'}")

    if high_performance:
        max_workers = args.max_workers or os.cpu_count()
        print(f"💻 CPU cores: {os.cpu_count()} detected, using {max_workers} workers")
        print(f"📦 Batch size: {args.batch_size}")

    print()

    # Run the processing
    try:
        main_with_parameters(
            datasheet_path=args.source,
            attachments_dir=args.attachments,
            output_dir=args.output,
            high_performance=high_performance,
            max_workers=args.max_workers,
            batch_size=args.batch_size
        )
    except KeyboardInterrupt:
        print("\n⚠️  Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error during processing: {e}")
        sys.exit(1)
