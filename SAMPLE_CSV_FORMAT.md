# Sample CSV Format for Export File Processing

This document shows the expected CSV format for the enhanced export file processing script.

## Required CSV Columns

The CSV files in your Export folders must contain at least these two columns:

- **File**: The carved filename in the Attachments folder
- **Source**: The original full path where the file was found

## Sample CSV Structure

```csv
File,Source,Size,Modified,Created
.\Attachments\0000001_Carved.xlsx,SUBHAM_LT01_HDD01_DECRYPTED.E01 - Partition 3 (Microsoft NTFS, 951.65 GB) Windows\Users\subha\Downloads\document.xlsx,15234,2024-01-15 10:30:22,2024-01-15 09:45:10
.\Attachments\0000002_Carved.docx,SUBHAM_LT01_HDD01_DECRYPTED.E01 - Partition 3 (Microsoft NTFS, 951.65 GB) Windows\Users\subha\Documents\report.docx,28456,2024-01-16 14:22:33,2024-01-16 14:20:15
.\Attachments\0000003_Carved.pdf,SUBHAM_LT01_HDD01_DECRYPTED.E01 - Partition 3 (Microsoft NTFS, 951.65 GB) Windows\Users\subha\Desktop\presentation.pdf,1245678,2024-01-17 16:45:12,2024-01-17 16:40:30
```

## How the Script Processes This Data

### 1. Carved Filename Extraction
- From `File` column: `.\Attachments\0000001_Carved.xlsx`
- Extracted: `0000001_Carved.xlsx`
- This is the actual file in the Attachments folder

### 2. Origin Filename Extraction  
- From `Source` column: `...Windows\Users\subha\Downloads\document.xlsx`
- Extracted: `document.xlsx`
- This becomes the final renamed filename

### 3. Path Transformation
- Original: `SUBHAM_LT01_HDD01_DECRYPTED.E01 - Partition 3 (Microsoft NTFS, 951.65 GB) Windows\Users\subha\Downloads\document.xlsx`
- Cleaned: `C:\Windows\Users\subha\Downloads\document.xlsx`
- Folder created: `Export\Windows\Users\subha\Downloads\`

### 4. File Processing Result
- Source file: `Export\Attachments\0000001_Carved.xlsx`
- Destination: `Export\Windows\Users\subha\Downloads\document.xlsx`
- Action: Move and rename from carved filename to original filename

## Excel Log Output

The script will log each file with these details:

| Carved Filename | Origin Filename | Replaced Filename | Origin Folder Path | Replaced Folder Path | Error Status |
|----------------|----------------|-------------------|-------------------|---------------------|--------------|
| 0000001_Carved.xlsx | document.xlsx | document.xlsx | C:\Windows\Users\subha\Downloads | Export\Windows\Users\subha\Downloads | No error |
| 0000002_Carved.docx | report.docx | report.docx | C:\Windows\Users\subha\Documents | Export\Windows\Users\subha\Documents | No error |

## Notes

- Additional columns in the CSV (like Size, Modified, Created) are ignored
- The script only requires `File` and `Source` columns to function
- File extensions are preserved during the renaming process
- Duplicate filenames in the same destination folder are handled automatically
