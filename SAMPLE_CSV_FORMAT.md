# Sample CSV Format for Export File Processing

This document shows the expected CSV format for the enhanced export file processing script.

## Required CSV Columns

The CSV files in your Export folders must contain these three required columns:

- **Carved Filename**: The carved filename in the Attachments folder (e.g., `0104731_Carved.png`)
- **Filename**: The original filename (e.g., `Testing_123.png`)
- **Source**: The original full path where the file was found

## Sample CSV Structure

```csv
Carved Filename,Filename,Source,Size,Modified,Created
0104731_Carved.png,Testing_123.png,TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) WinRE_DRV\Recovery\WindowsRE\winre$#*.wim,15234,2024-01-15 10:30:22,2024-01-15 09:45:10
0104732_Carved.docx,Report*Document?.docx,TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) Windows\Users\subha\Documents\report<file>.docx,28456,2024-01-16 14:22:33,2024-01-16 14:20:15
0104733_Carved.pdf,Presentation|File.pdf,TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) Windows\Users\subha\Desktop\presentation"file".pdf,1245678,2024-01-17 16:45:12,2024-01-17 16:40:30
```

## How the Script Processes This Data

### 1. Carved Filename Extraction
- From `Carved Filename` column: `0104731_Carved.png`
- This is the actual file in the Attachments folder

### 2. Origin Filename Extraction
- From `Filename` column: `Testing_123.png`
- Special characters are sanitized: `Report*Document?.docx` → `Report_Document_.docx`
- This becomes the final renamed filename

### 3. Path Transformation and Sanitization
- Original: `TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) WinRE_DRV\Recovery\WindowsRE\winre$#*.wim`
- Cleaned: `C:\WinRE_DRV\Recovery\WindowsRE\winre___.wim`
- Prohibited characters (`\ / : * ? " < > |`) are replaced with underscores
- Folder created: `Export\WinRE_DRV\Recovery\WindowsRE\`

### 4. File Processing Result
- Source file: `Export\Attachments\0104731_Carved.png`
- Destination: `Export\WinRE_DRV\Recovery\WindowsRE\Testing_123.png`
- Action: Move and rename from carved filename to sanitized original filename

### 5. Special Character Handling
The script automatically handles prohibited Windows filename characters:
- `Report*Document?.docx` → `Report_Document_.docx`
- `presentation"file".pdf` → `presentation_file_.pdf`
- `winre$#*.wim` → `winre___.wim`

## Excel Log Output

The script will log each file with these details:

| Carved Filename | Origin Filename | Replaced Filename | Origin Folder Path | Replaced Folder Path | Error Status |
|----------------|----------------|-------------------|-------------------|---------------------|--------------|
| 0104731_Carved.png | Testing_123.png | Testing_123.png | TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) WinRE_DRV\Recovery\WindowsRE\winre$#*.wim | Export\WinRE_DRV\Recovery\WindowsRE | No error |
| 0104732_Carved.docx | Report*Document?.docx | Report_Document_.docx | TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) Windows\Users\subha\Documents\report<file>.docx | Export\Windows\Users\subha\Documents | No error |

## Notes

- Additional columns in the CSV (like Size, Modified, Created) are ignored
- The script requires `Carved Filename`, `Filename`, and `Source` columns to function
- File extensions are preserved during the renaming process
- Prohibited characters in filenames and paths are automatically replaced with underscores
- Duplicate filenames in the same destination folder are handled automatically with numbering
- Original folder paths in the log show the raw source data, while replaced folder paths show the sanitized destination
