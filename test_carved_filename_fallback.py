#!/usr/bin/env python3
"""
Test script to demonstrate the new carved filename fallback functionality.
"""

import os
import csv
import tempfile
import shutil
from pathlib import Path

def create_test_environment_with_fallback():
    """Create a test environment with various filename scenarios."""
    # Create temporary directory
    test_dir = tempfile.mkdtemp(prefix="export_test_fallback_")
    print(f"Creating test environment in: {test_dir}")
    
    # Create attachments directory
    attachments_dir = os.path.join(test_dir, "Attachments")
    os.makedirs(attachments_dir, exist_ok=True)
    
    # Create sample CSV datasheet with various scenarios
    csv_data = [
        {
            'Carved Filename': '0001_Carved.txt',
            'FileName': 'Document_Test.txt',  # Has both carved and origin filename
            'Source': 'TEST_DRIVE.E01 - Partition 1 (NTFS, 500 GB) Windows\\Users\\test\\Documents\\document.txt',
            'Artifacts Type': 'Documents',
            'Size': '1024'
        },
        {
            'Carved Filename': '0002_Carved.jpg',
            'FileName': '',  # Empty origin filename - should use carved filename
            'Source': 'TEST_DRIVE.E01 - Partition 1 (NTFS, 500 GB) Windows\\Users\\test\\Pictures\\image.jpg',
            'Artifacts Type': 'Images',
            'Size': '2048'
        },
        {
            'Carved Filename': '',  # Empty carved filename - should be skipped
            'FileName': 'Missing_File.pdf',
            'Source': 'TEST_DRIVE.E01 - Partition 1 (NTFS, 500 GB) Windows\\Users\\test\\Downloads\\missing.pdf',
            'Artifacts Type': 'Documents',
            'Size': '4096'
        },
        {
            'Carved Filename': '0003_Carved.zip',
            'FileName': '',  # Empty origin filename - should use carved filename
            'Source': 'TEST_DRIVE.E01 - Partition 1 (NTFS, 500 GB) Windows\\Users\\test\\Downloads\\archive.zip',
            'Artifacts Type': 'Archives',
            'Size': '8192'
        },
        {
            'Carved Filename': '0004_Carved.mp4',
            'FileName': 'Video*File?.mp4',  # Has special characters - should be sanitized
            'Source': 'TEST_DRIVE.E01 - Partition 1 (NTFS, 500 GB) Windows\\Users\\test\\Videos\\video.mp4',
            'Artifacts Type': 'Videos',
            'Size': '16384'
        }
    ]
    
    # Write CSV file
    csv_file_path = os.path.join(test_dir, "Test_Fallback.csv")
    with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['Carved Filename', 'FileName', 'Source', 'Artifacts Type', 'Size']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(csv_data)
    
    # Create actual test files in Attachments (only for non-empty carved filenames)
    test_files_created = 0
    for row in csv_data:
        if row['Carved Filename']:
            test_file_path = os.path.join(attachments_dir, row['Carved Filename'])
            with open(test_file_path, 'w') as f:
                f.write(f"Test content for {row['Carved Filename']}")
            test_files_created += 1
    
    print(f"✅ Test environment created:")
    print(f"   📁 Directory: {test_dir}")
    print(f"   📄 CSV file: {csv_file_path}")
    print(f"   📦 Attachments: {attachments_dir}")
    print(f"   📊 CSV entries: {len(csv_data)}")
    print(f"   📁 Files created: {test_files_created}")
    
    # Print scenarios
    print(f"\n📋 Test Scenarios:")
    for i, row in enumerate(csv_data, 1):
        carved = row['Carved Filename']
        origin = row['FileName']
        if not carved:
            print(f"   {i}. Empty carved filename → Should be SKIPPED")
        elif not origin:
            print(f"   {i}. {carved} (no origin) → Should use CARVED filename")
        else:
            print(f"   {i}. {carved} → {origin} (normal processing)")
    
    return test_dir, csv_file_path, attachments_dir

def run_fallback_test():
    """Run a test of the carved filename fallback functionality."""
    print("🧪 Testing Carved Filename Fallback Functionality")
    print("=" * 60)
    
    # Create test environment
    test_dir, csv_file, attachments_dir = create_test_environment_with_fallback()
    
    try:
        # Change to test directory
        original_cwd = os.getcwd()
        os.chdir(test_dir)
        
        # Import and run the processing
        import sys
        sys.path.insert(0, original_cwd)
        from process_export_files import main_with_parameters
        
        print(f"\n🚀 Running fallback processing test...")
        print(f"   CSV: {csv_file}")
        print(f"   Attachments: {attachments_dir}")
        print(f"   Output: Organized_Files (default)")
        
        # Run the processing
        main_with_parameters(
            datasheet_path=csv_file,
            attachments_dir=attachments_dir,
            output_dir=None,  # Use default
            high_performance=False,  # Use standard mode for clearer output
            max_workers=1,
            batch_size=10,
            enable_long_paths=True
        )
        
        # Check results
        output_dir = os.path.join(test_dir, "Organized_Files")
        if os.path.exists(output_dir):
            print(f"\n📊 Results:")
            print(f"   Output directory created: {output_dir}")
            
            # Count organized files
            organized_files = []
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    if not file.endswith('.xlsx'):  # Exclude log files
                        organized_files.append(os.path.join(root, file))
            
            print(f"   Files organized: {len(organized_files)}")
            
            # List organized files with their final names
            for file_path in organized_files:
                rel_path = os.path.relpath(file_path, output_dir)
                filename = os.path.basename(file_path)
                print(f"     📄 {filename} → {rel_path}")
            
            # Check for Excel log
            log_files = [f for f in os.listdir(test_dir) if f.startswith('File_Processing_Log_') and f.endswith('.xlsx')]
            if log_files:
                print(f"   Excel log created: {log_files[0]}")
        
        print(f"\n✅ Carved filename fallback test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        os.chdir(original_cwd)
        # Clean up
        try:
            shutil.rmtree(test_dir)
            print(f"🧹 Cleaned up test directory: {test_dir}")
        except Exception as e:
            print(f"⚠️  Error cleaning up {test_dir}: {e}")

if __name__ == "__main__":
    run_fallback_test()
