#!/usr/bin/env python3
"""
Performance testing script for the enhanced export file processing.
"""

import os
import csv
import time
import tempfile
import shutil
from pathlib import Path

def create_large_test_dataset(num_files: int = 1000, temp_dir: str = None):
    """
    Create a large test dataset for performance testing.
    
    Args:
        num_files: Number of test files to create
        temp_dir: Temporary directory for test data
        
    Returns:
        Path to the test directory
    """
    if temp_dir is None:
        temp_dir = tempfile.mkdtemp(prefix="export_test_")
    
    print(f"Creating test dataset with {num_files} files in {temp_dir}")
    
    # Create Export directory structure
    export_dir = os.path.join(temp_dir, "Export")
    attachments_dir = os.path.join(export_dir, "Attachments")
    os.makedirs(attachments_dir, exist_ok=True)
    
    # Create test CSV data
    csv_data = []
    artifacts_types = ["Images", "Documents", "Archives", "Videos", "Audio"]
    
    for i in range(num_files):
        # Create some empty carved filenames for testing
        carved_filename = f"{i:07d}_Carved.txt" if i % 50 != 0 else ""
        
        csv_data.append({
            'Carved Filename': carved_filename,
            'Filename': f'Test*File_{i}?.txt',
            'Source': f'TEST_DRIVE.E01 - Partition 1 (NTFS, 500 GB) Windows\\Users\\test\\Documents\\folder_{i % 100}\\test"file"_{i}.txt',
            'Artifacts type': artifacts_types[i % len(artifacts_types)],
            'Size': str(1024 + (i * 100)),
            'Modified': '2024-01-15 10:30:22',
            'Created': '2024-01-15 09:45:10'
        })
        
        # Create actual test files in Attachments (only for non-empty carved filenames)
        if carved_filename:
            test_file_path = os.path.join(attachments_dir, carved_filename)
            with open(test_file_path, 'w') as f:
                f.write(f"Test content for file {i}")
    
    # Write CSV file
    csv_file_path = os.path.join(export_dir, "Test_Documents.csv")
    with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['Carved Filename', 'Filename', 'Source', 'Artifacts type', 'Size', 'Modified', 'Created']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(csv_data)
    
    print(f"✅ Test dataset created:")
    print(f"   📁 Directory: {export_dir}")
    print(f"   📄 CSV file: {csv_file_path}")
    print(f"   📦 Files in Attachments: {len([f for f in os.listdir(attachments_dir) if f.endswith('.txt')])}")
    print(f"   📊 Total CSV entries: {len(csv_data)}")
    print(f"   ⚠️  Empty carved filenames: {len([d for d in csv_data if not d['Carved Filename']])}")
    
    return temp_dir

def run_performance_test(test_dir: str, mode: str = "high_performance"):
    """
    Run performance test on the created dataset.
    
    Args:
        test_dir: Test directory path
        mode: "high_performance" or "standard"
    """
    print(f"\n🚀 Running performance test in {mode.upper()} mode")
    print("=" * 60)
    
    # Change to test directory
    original_cwd = os.getcwd()
    os.chdir(test_dir)
    
    try:
        start_time = time.time()
        
        if mode == "high_performance":
            from process_export_files import main_high_performance
            main_high_performance()
        else:
            from process_export_files import main_standard
            main_standard()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n⏱️  {mode.upper()} MODE RESULTS:")
        print(f"   Total execution time: {total_time:.2f} seconds")
        
        return total_time
        
    finally:
        os.chdir(original_cwd)

def cleanup_test_data(test_dir: str):
    """Clean up test data directory."""
    try:
        shutil.rmtree(test_dir)
        print(f"🧹 Cleaned up test directory: {test_dir}")
    except Exception as e:
        print(f"⚠️  Error cleaning up {test_dir}: {e}")

def main():
    """Main performance testing function."""
    print("🧪 Export File Processing - Performance Testing")
    print("=" * 50)
    
    # Test with different dataset sizes
    test_sizes = [100, 500, 1000]
    
    for size in test_sizes:
        print(f"\n📊 Testing with {size} files...")
        
        # Create test dataset
        test_dir = create_large_test_dataset(size)
        
        try:
            # Test high-performance mode
            hp_time = run_performance_test(test_dir, "high_performance")
            
            # Reset for standard mode test
            # Note: We'd need to recreate the dataset for a fair comparison
            # since files have been moved in the first test
            
            print(f"\n📈 PERFORMANCE SUMMARY for {size} files:")
            print(f"   High-Performance Mode: {hp_time:.2f} seconds")
            print(f"   Processing Rate: {size/hp_time:.2f} files/second")
            
        finally:
            cleanup_test_data(test_dir)
    
    print("\n🎯 Performance testing completed!")
    print("\nRecommendations:")
    print("  - Use high-performance mode for datasets > 500 files")
    print("  - Ensure sufficient RAM for large datasets")
    print("  - SSD storage provides better performance than HDD")

if __name__ == "__main__":
    main()
