#!/usr/bin/env python3
"""
Test script to demonstrate the new parameter-based functionality.
"""

import os
import csv
import tempfile
import shutil
from pathlib import Path

def create_test_environment():
    """Create a test environment with sample datasheet and attachments."""
    # Create temporary directory
    test_dir = tempfile.mkdtemp(prefix="export_test_params_")
    print(f"Creating test environment in: {test_dir}")
    
    # Create attachments directory
    attachments_dir = os.path.join(test_dir, "Attachments")
    os.makedirs(attachments_dir, exist_ok=True)
    
    # Create sample CSV datasheet
    csv_data = [
        {
            'Carved Filename': '0001_Carved.txt',
            'Filename': 'Document*Test?.txt',
            'Source': 'TEST_DRIVE.E01 - Partition 1 (NTFS, 500 GB) Windows\\Users\\test\\Documents\\folder_1\\test"file"_1.txt',
            'Artifacts type': 'Documents',
            'Size': '1024',
            'Modified': '2024-01-15 10:30:22',
            'Created': '2024-01-15 09:45:10'
        },
        {
            'Carved Filename': '0002_Carved.jpg',
            'Filename': 'Image<File>.jpg',
            'Source': 'TEST_DRIVE.E01 - Partition 1 (NTFS, 500 GB) Windows\\Users\\test\\Pictures\\vacation\\image|file|.jpg',
            'Artifacts type': 'Images',
            'Size': '2048',
            'Modified': '2024-01-16 14:22:33',
            'Created': '2024-01-16 14:20:15'
        },
        {
            'Carved Filename': '',  # Empty carved filename
            'Filename': 'Missing_File.pdf',
            'Source': 'TEST_DRIVE.E01 - Partition 1 (NTFS, 500 GB) Windows\\Users\\test\\Downloads\\missing.pdf',
            'Artifacts type': 'Documents',
            'Size': '4096',
            'Modified': '2024-01-17 16:45:12',
            'Created': '2024-01-17 16:40:30'
        },
        {
            'Carved Filename': '0003_Carved.zip',
            'Filename': 'Archive:File.zip',
            'Source': 'TEST_DRIVE.E01 - Partition 1 (NTFS, 500 GB) Windows\\Users\\test\\Downloads\\archive$file#.zip',
            'Artifacts type': 'Archives',
            'Size': '8192',
            'Modified': '2024-01-18 09:15:45',
            'Created': '2024-01-18 09:10:20'
        }
    ]
    
    # Write CSV file
    csv_file_path = os.path.join(test_dir, "Test_Datasheet.csv")
    with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['Carved Filename', 'Filename', 'Source', 'Artifacts type', 'Size', 'Modified', 'Created']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(csv_data)
    
    # Create actual test files in Attachments (only for non-empty carved filenames)
    test_files_created = 0
    for row in csv_data:
        if row['Carved Filename']:
            test_file_path = os.path.join(attachments_dir, row['Carved Filename'])
            with open(test_file_path, 'w') as f:
                f.write(f"Test content for {row['Carved Filename']}")
            test_files_created += 1
    
    print(f"✅ Test environment created:")
    print(f"   📁 Directory: {test_dir}")
    print(f"   📄 CSV file: {csv_file_path}")
    print(f"   📦 Attachments: {attachments_dir}")
    print(f"   📊 CSV entries: {len(csv_data)}")
    print(f"   📁 Files created: {test_files_created}")
    print(f"   ⚠️  Empty carved filenames: {len([d for d in csv_data if not d['Carved Filename']])}")
    
    return test_dir, csv_file_path, attachments_dir

def run_parameter_test():
    """Run a test of the parameter-based functionality."""
    print("🧪 Testing Parameter-Based Export File Processing")
    print("=" * 60)
    
    # Create test environment
    test_dir, csv_file, attachments_dir = create_test_environment()
    
    try:
        # Change to test directory
        original_cwd = os.getcwd()
        os.chdir(test_dir)
        
        # Import and run the processing
        import sys
        sys.path.insert(0, original_cwd)
        from process_export_files import main_with_parameters
        
        print(f"\n🚀 Running parameter-based processing...")
        print(f"   CSV: {csv_file}")
        print(f"   Attachments: {attachments_dir}")
        print(f"   Output: Organized_Files (default)")
        
        # Run the processing
        main_with_parameters(
            datasheet_path=csv_file,
            attachments_dir=attachments_dir,
            output_dir=None,  # Use default
            high_performance=True,
            max_workers=2,  # Use fewer workers for testing
            batch_size=10
        )
        
        # Check results
        output_dir = os.path.join(test_dir, "Organized_Files")
        if os.path.exists(output_dir):
            print(f"\n📊 Results:")
            print(f"   Output directory created: {output_dir}")
            
            # Count organized files
            organized_files = []
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    if not file.endswith('.xlsx'):  # Exclude log files
                        organized_files.append(os.path.join(root, file))
            
            print(f"   Files organized: {len(organized_files)}")
            
            # List organized files
            for file_path in organized_files:
                rel_path = os.path.relpath(file_path, output_dir)
                print(f"     📄 {rel_path}")
            
            # Check for Excel log
            log_files = [f for f in os.listdir(test_dir) if f.startswith('File_Processing_Log_') and f.endswith('.xlsx')]
            if log_files:
                print(f"   Excel log created: {log_files[0]}")
        
        print(f"\n✅ Parameter-based processing test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        os.chdir(original_cwd)
        # Clean up
        try:
            shutil.rmtree(test_dir)
            print(f"🧹 Cleaned up test directory: {test_dir}")
        except Exception as e:
            print(f"⚠️  Error cleaning up {test_dir}: {e}")

def demonstrate_usage():
    """Demonstrate various usage patterns."""
    print("\n📖 Usage Examples:")
    print("=" * 40)
    
    examples = [
        "# Basic usage with CSV",
        "python process_export_files.py -s data.csv -a /path/to/attachments",
        "",
        "# Excel datasheet with custom output",
        "python process_export_files.py -s Compiled_test.xlsx -a D:\\Project\\Attachments -o MyOutput",
        "",
        "# Standard mode (single-threaded)",
        "python process_export_files.py -s data.csv -a /path/to/attachments --standard",
        "",
        "# Custom performance settings",
        "python process_export_files.py -s data.csv -a /path/to/attachments --max-workers 4 --batch-size 50",
        "",
        "# Legacy mode (old behavior)",
        "python process_export_files.py --legacy",
        "",
        "# Show help",
        "python process_export_files.py --help"
    ]
    
    for example in examples:
        if example.startswith("#"):
            print(f"\n{example}")
        elif example:
            print(f"  {example}")
        else:
            print()

if __name__ == "__main__":
    run_parameter_test()
    demonstrate_usage()
