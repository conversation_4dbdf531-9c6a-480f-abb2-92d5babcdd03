{\rtf1\ansi\ansicpg1252\deff0\deflang1033{\fonttbl{\f0\froman\fprq2 Times New Roman;}}
{\*\generator Msftedit 5.41.21.2500;}\viewkind4\uc1\pard\f0\fs20       
\par
      7-Zip Copyright (C) 1999-2024 <PERSON>.\par
\par
      Licenses for files are:\par
\par
        1) 7z.dll :  GNU LGPL + unRAR restriction\par
        2) All other files: GNU LGPL\par
\par      
      The GNU LGPL + unRAR restriction means that\par 
      you must follow GNU LGPL rules and unRAR restriction rules.\par
\par
\par
      Notes:\par 
        You can use 7-Zip on any computer, including a computer in commercial\par
        organization. You don't need to register or pay for 7-Zip.\par
\par
\par
      GNU LGPL information\par
      --------------------\par
\par
        This library is free software; you can redistribute it and/or\par
        modify it under the terms of the GNU Lesser General Public\par
        License as published by the Free Software Foundation; either\par
        version 2.1 of the License, or (at your option) any later version.\par
\par
        This library is distributed in the hope that it will be useful,\par
        but WITHOUT ANY WARRANTY; without even the implied warranty of\par
        MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU\par
        Lesser General Public License for more details.\par
\par
        You should have received a copy of the GNU Lesser General Public\par
        License along with this library; if not, write to the Free Software\par
        Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA\par
\par
\par
      unRAR restriction\par
      -----------------\par
        The decompression engine for RAR archives was developed using\par
        source code of unRAR program.\par
        All copyrights to original unRAR code are owned by Alexander Roshal.\par
\par
        The license for original unRAR code has the following restriction:\par
\par
        The unRAR sources cannot be used to re-create the RAR\par
        compression algorithm, which is proprietary. Distribution\par
        of modified unRAR sources in separate formor as a part of\par
        other software is permitted, provided that it is clearly\par
        stated in the documentation and source comments that the\par
        code may not be used to develop a RAR (WinRAR) compatible\par
        archiver.\par
\par
\par
}