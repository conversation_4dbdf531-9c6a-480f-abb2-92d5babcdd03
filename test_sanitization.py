#!/usr/bin/env python3
"""
Test script to demonstrate the special character sanitization functionality.
"""

from process_export_files import sanitize_filename, sanitize_path, clean_source_path

def test_sanitization():
    """Test the sanitization functions with various special characters."""
    print("Testing Special Character Sanitization")
    print("=" * 50)
    
    # Test filename sanitization
    print("\n1. Filename Sanitization:")
    test_filenames = [
        "Testing_123.png",
        "Report*Document?.docx", 
        'presentation"file".pdf',
        "file<name>.txt",
        "document|version.xlsx",
        "path\\file.doc",
        "url/file.html",
        "time:stamp.log"
    ]
    
    for filename in test_filenames:
        sanitized = sanitize_filename(filename)
        print(f"  '{filename}' → '{sanitized}'")
    
    # Test path sanitization
    print("\n2. Path Sanitization:")
    test_paths = [
        r"C:\Windows\Users\subha\Downloads\file.xlsx",
        r"C:\Program Files\App*Name\config?.ini",
        r'C:\Users\<USER>\Documents\file"name".txt',
        r"C:\Temp\folder<test>\file.log",
        r"C:\Data\folder|name\document.pdf"
    ]
    
    for path in test_paths:
        sanitized = sanitize_path(path)
        print(f"  '{path}' → '{sanitized}'")
    
    # Test full source path cleaning
    print("\n3. Full Source Path Cleaning:")
    test_sources = [
        "TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) WinRE_DRV\\Recovery\\WindowsRE\\winre$#*.wim",
        "SUBHAM_LT01_HDD01_DECRYPTED.E01 - Partition 3 (Microsoft NTFS, 951.65 GB) Windows\\Users\\subha\\Downloads\\file<test>.xlsx",
        'DATA_DRIVE.E01 - Partition 2 (NTFS, 500 GB) Program Files\\App*Name\\config"file".ini'
    ]
    
    for source in test_sources:
        cleaned = clean_source_path(source)
        print(f"  Original: {source}")
        print(f"  Cleaned:  {cleaned}")
        print()

if __name__ == "__main__":
    test_sanitization()
