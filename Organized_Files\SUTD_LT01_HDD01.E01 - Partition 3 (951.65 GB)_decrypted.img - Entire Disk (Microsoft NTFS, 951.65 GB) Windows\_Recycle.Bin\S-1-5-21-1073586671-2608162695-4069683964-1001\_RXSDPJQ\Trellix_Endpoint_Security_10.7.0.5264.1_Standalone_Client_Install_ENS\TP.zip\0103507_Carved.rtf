{\rtf1\ansi\ansicpg1252\uc1 \deff0\deflang1033\deflangfe1033{\fonttbl{\f0\froman\fcharset0\fprq2{\*\panose 02020603050405020304}Times New Roman{\*\falt Times};}{\f1\fswiss\fcharset0\fprq2{\*\panose 00009002190190021901}Arial;}
{\f3\froman\fcharset2\fprq2{\*\panose 0000f800000005200000}Symbol;}{\f6\fmodern\fcharset0\fprq1{\*\panose 00000000000000000000}Courier;}{\f15\fswiss\fcharset0\fprq2{\*\panose 020b0604030504040204}Tahoma;}
{\f25\froman\fcharset238\fprq2 Times New Roman CE{\*\falt Times};}{\f26\froman\fcharset204\fprq2 Times New Roman Cyr{\*\falt Times};}{\f28\froman\fcharset161\fprq2 Times New Roman Greek{\*\falt Times};}
{\f29\froman\fcharset162\fprq2 Times New Roman Tur{\*\falt Times};}{\f30\froman\fcharset186\fprq2 Times New Roman Baltic{\*\falt Times};}{\f31\fswiss\fcharset238\fprq2 Arial CE;}{\f32\fswiss\fcharset204\fprq2 Arial Cyr;}
{\f34\fswiss\fcharset161\fprq2 Arial Greek;}{\f35\fswiss\fcharset162\fprq2 Arial Tur;}{\f36\fswiss\fcharset186\fprq2 Arial Baltic;}{\f115\fswiss\fcharset238\fprq2 Tahoma CE;}{\f116\fswiss\fcharset204\fprq2 Tahoma Cyr;}
{\f118\fswiss\fcharset161\fprq2 Tahoma Greek;}{\f119\fswiss\fcharset162\fprq2 Tahoma Tur;}{\f120\fswiss\fcharset186\fprq2 Tahoma Baltic;}}{\colortbl;\red0\green0\blue0;\red0\green0\blue255;\red0\green255\blue255;\red0\green255\blue0;
\red255\green0\blue255;\red255\green0\blue0;\red255\green255\blue0;\red255\green255\blue255;\red0\green0\blue128;\red0\green128\blue128;\red0\green128\blue0;\red128\green0\blue128;\red128\green0\blue0;\red128\green128\blue0;\red128\green128\blue128;
\red192\green192\blue192;}{\stylesheet{\widctlpar\adjustright \fs20\cgrid \snext0 Normal;}{\s1\keepn\nowidctlpar\outlinelevel0\adjustright \b\f1\fs16 \sbasedon0 \snext0 heading 1;}{\*\cs10 \additive Default Paragraph Font;}{\s15\nowidctlpar\adjustright 
\f6\fs16\cf6 \sbasedon0 \snext15 Body Text;}}{\*\listtable{\list\listtemplateid67698689\listsimple{\listlevel\levelnfc23\leveljc0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01\u-3913 ?;}{\levelnumbers;}\f3\fbias0 \fi-360\li360
\jclisttab\tx360 }{\listname ;}\listid143089650}{\list\listtemplateid67698703\listsimple{\listlevel\levelnfc0\leveljc0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'02\'00.;}{\levelnumbers\'01;}\fi-360\li360\jclisttab\tx360 }{\listname 
;}\listid461312141}{\list\listtemplateid67698689\listsimple{\listlevel\levelnfc23\leveljc0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01\u-3913 ?;}{\levelnumbers;}\f3\fbias0 \fi-360\li360\jclisttab\tx360 }{\listname ;}\listid777650178
}{\list\listtemplateid67698689\listsimple{\listlevel\levelnfc23\leveljc0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01\u-3913 ?;}{\levelnumbers;}\f3\fbias0 \fi-360\li360\jclisttab\tx360 }{\listname ;}\listid1040588003}
{\list\listtemplateid67698689\listsimple{\listlevel\levelnfc23\leveljc0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01\u-3913 ?;}{\levelnumbers;}\f3\fbias0 \fi-360\li360\jclisttab\tx360 }{\listname ;}\listid1127820404}
{\list\listtemplateid67698689\listsimple{\listlevel\levelnfc23\leveljc0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01\u-3913 ?;}{\levelnumbers;}\f3\fbias0 \fi-360\li360\jclisttab\tx360 }{\listname ;}\listid1838302842}}
{\*\listoverridetable{\listoverride\listid1040588003\listoverridecount0\ls1}{\listoverride\listid1838302842\listoverridecount0\ls2}{\listoverride\listid777650178\listoverridecount0\ls3}{\listoverride\listid143089650\listoverridecount0\ls4}
{\listoverride\listid1127820404\listoverridecount0\ls5}{\listoverride\listid461312141\listoverridecount0\ls6}}{\info{\author Tom Tokarski}{\operator Stephen Brandt}{\creatim\yr1999\mo6\dy25\hr13\min34}{\revtim\yr2000\mo5\dy23\hr15\min38}{\version10}
{\edmins14}{\nofpages1}{\nofwords322}{\nofchars1838}{\*\company InstallShield Software Corporation}{\nofcharsws0}{\vern113}}\widowctrl\ftnbj\aenddoc\hyphcaps0\viewkind1\viewscale100 \fet0\sectd \linex0\sectdefaultcl {\*\pnseclvl1
\pnucrm\pnstart1\pnindent720\pnhang{\pntxta .}}{\*\pnseclvl2\pnucltr\pnstart1\pnindent720\pnhang{\pntxta .}}{\*\pnseclvl3\pndec\pnstart1\pnindent720\pnhang{\pntxta .}}{\*\pnseclvl4\pnlcltr\pnstart1\pnindent720\pnhang{\pntxta )}}{\*\pnseclvl5
\pndec\pnstart1\pnindent720\pnhang{\pntxtb (}{\pntxta )}}{\*\pnseclvl6\pnlcltr\pnstart1\pnindent720\pnhang{\pntxtb (}{\pntxta )}}{\*\pnseclvl7\pnlcrm\pnstart1\pnindent720\pnhang{\pntxtb (}{\pntxta )}}{\*\pnseclvl8\pnlcltr\pnstart1\pnindent720\pnhang
{\pntxtb (}{\pntxta )}}{\*\pnseclvl9\pnlcrm\pnstart1\pnindent720\pnhang{\pntxtb (}{\pntxta )}}\pard\plain \s15\nowidctlpar\adjustright \f6\fs16\cf6 {\f15\cf0 
\par To add your own license text to this dialog, specify your license agreement file in the Dialog editor.  
\par 
\par {\pntext\pard\plain\s15 \f15\fs16 \hich\af15\dbch\af0\loch\f15 1.\tab}}\pard \s15\fi-360\li360\nowidctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls6\pnrnot0\pndec\pnstart1\pnindent360\pnhang{\pntxta .}}\ls6\adjustright {\f15\cf0 Navigate to the }{
\b\f15\cf0 User Interface}{\f15\cf0  view.
\par {\pntext\pard\plain\s15 \f15\fs16 \hich\af15\dbch\af0\loch\f15 2.\tab}}\pard \s15\fi-360\li360\nowidctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls6\pnrnot0\pndec\pnstart1\pnindent360\pnhang{\pntxta .}}\ls6\adjustright {\f15\cf0 Select the }{\b\f15\cf0 
LicenseAgreement}{\f15\cf0  dialog.
\par {\pntext\pard\plain\s15 \f15\fs16 \hich\af15\dbch\af0\loch\f15 3.\tab}}\pard \s15\fi-360\li360\nowidctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls6\pnrnot0\pndec\pnstart1\pnindent360\pnhang{\pntxta .}}\ls6\adjustright {\f15\cf0 Choose to edit the }{
\b\f15\cf0 dialog layout}{\f15\cf0 . 
\par {\pntext\pard\plain\s15 \f15\fs16 \hich\af15\dbch\af0\loch\f15 4.\tab}}\pard \s15\fi-360\li360\nowidctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls6\pnrnot0\pndec\pnstart1\pnindent360\pnhang{\pntxta .}}\ls6\adjustright {\f15\cf0 Once in the D
ialog editor, select the }{\b\f15\cf0 Memo}{\f15\cf0  ScrollableText control.
\par {\pntext\pard\plain\s15 \f15\fs16 \hich\af15\dbch\af0\loch\f15 5.\tab}}\pard \s15\fi-360\li360\nowidctlpar\jclisttab\tx360{\*\pn \pnlvlbody\ilvl0\ls6\pnrnot0\pndec\pnstart1\pnindent360\pnhang{\pntxta .}}\ls6\adjustright {\f15\cf0 Set }{\b\f15\cf0 FileName
}{\f15\cf0  to the name of your license agreement RTF file. 
\par }\pard \s15\nowidctlpar\adjustright {\f15\cf0 
\par After you build your release, your license text will be displayed in the License Agreement dialog.
\par }\pard\plain \nowidctlpar\adjustright \fs20\cgrid {\f1\fs16\cgrid0 
\par }}