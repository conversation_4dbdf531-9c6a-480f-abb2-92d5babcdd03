#!/usr/bin/env python3
"""
Test script to demonstrate CSV processing with the correct column headers.
"""

import csv
import os
from process_export_files import extract_file_info_from_csv

def create_sample_csv():
    """Create a sample CSV file with the correct column structure."""
    sample_data = [
        {
            'Carved Filename': '0104731_Carved.png',
            'Filename': 'Testing_123.png',
            'Source': 'TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) WinRE_DRV\\Recovery\\WindowsRE\\winre$#*.wim',
            'Size': '15234',
            'Modified': '2024-01-15 10:30:22',
            'Created': '2024-01-15 09:45:10'
        },
        {
            'Carved Filename': '0104732_Carved.docx',
            'Filename': 'Report*Document?.docx',
            'Source': 'TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) Windows\\Users\\subha\\Documents\\report<file>.docx',
            'Size': '28456',
            'Modified': '2024-01-16 14:22:33',
            'Created': '2024-01-16 14:20:15'
        },
        {
            'Carved Filename': '0104733_Carved.pdf',
            'Filename': 'Presentation|File.pdf',
            'Source': 'TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) Windows\\Users\\subha\\Desktop\\presentation"file".pdf',
            'Size': '1245678',
            'Modified': '2024-01-17 16:45:12',
            'Created': '2024-01-17 16:40:30'
        }
    ]
    
    filename = 'sample_test.csv'
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['Carved Filename', 'Filename', 'Source', 'Size', 'Modified', 'Created']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(sample_data)
    
    return filename

def test_csv_processing():
    """Test the CSV processing functionality."""
    print("Testing CSV Processing with Correct Column Headers")
    print("=" * 60)
    
    # Create sample CSV
    csv_file = create_sample_csv()
    print(f"Created sample CSV: {csv_file}")
    
    # Test extraction
    print("\nExtracting file information from CSV:")
    file_info = extract_file_info_from_csv(csv_file)
    
    print(f"Found {len(file_info)} entries:")
    print()
    
    for i, (carved, origin, cleaned_source, original_source) in enumerate(file_info, 1):
        print(f"Entry {i}:")
        print(f"  Carved Filename: {carved}")
        print(f"  Origin Filename: {origin}")
        print(f"  Original Source: {original_source}")
        print(f"  Cleaned Source:  {cleaned_source}")
        print()
    
    # Clean up
    os.remove(csv_file)
    print(f"Cleaned up: {csv_file}")

if __name__ == "__main__":
    test_csv_processing()
