# Export Folder File Organization Scripts

This repository contains Python scripts to organize files from Export folders based on their source paths in CSV files.

## Overview

The scripts process CSV files in Export folders, extract source path information, create corresponding folder structures, and move files from Attachments folders to their organized locations.

## Files

### 1. `process_export_files.py` - Main Processing Script

**Purpose**: Organizes files from Export folders based on CSV source paths.

**What it does**:
- Reads CSV files from `Export` and `Export (1)` folders
- Extracts `Source` column values and transforms them:
  - Removes partition information (e.g., "SUBHAM_LT01_HDD01_DECRYPTED.E01 - Partition 3 (Microsoft NTFS, 951.65 GB)")
  - Replaces with "C:\" prefix
- Creates unique folder paths (ignores duplicates)
- Handles extremely long Windows paths by creating simplified versions with hash identifiers
- **NEW**: Sanitizes filenames and paths by replacing prohibited characters (`\ / : * ? " < > | $ #`) with underscores
- Renames files from carved format to original filenames based on CSV data
- Moves files from `Attachments` folders to corresponding organized structure
- **NEW**: Generates comprehensive Excel logs with detailed tracking
- Provides detailed summary report

**Usage**:
```bash
python process_export_files.py
```

**Installation & Requirements**:
```bash
# Check if all requirements are satisfied
python check_requirements.py

# Install Excel logging support (optional but recommended)
pip install openpyxl
```

**Example transformation**:
- **CSV Data**:
  - Carved Filename: `0104731_Carved.png`
  - Filename: `Testing*File?.png`
  - Source: `TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) WinRE_DRV\Recovery\WindowsRE\winre$#*.wim`
- **Processing**:
  - Original source: `TESTING_LT01_HDD01.E01 - Partition 4 (Microsoft NTFS, 1.95 GB) WinRE_DRV\Recovery\WindowsRE\winre$#*.wim`
  - Cleaned path: `C:\WinRE_DRV\Recovery\WindowsRE\winre___.wim`
  - Sanitized filename: `Testing_File_.png`
  - Created folder: `Export\WinRE_DRV\Recovery\WindowsRE\`
  - Final result: `0104731_Carved.png` → `Testing_File_.png`

### 2. `restore_to_attachments.py` - Restoration Script

**Purpose**: Restores organized files back to Attachments folders.

**What it does**:
- Moves all files from organized folder structure back to `Attachments` folders
- Handles duplicate filenames by adding numbers
- Removes empty directories after restoration
- Provides confirmation prompt before proceeding

**Usage**:
```bash
python restore_to_attachments.py
```

### 3. `check_requirements.py` - Requirements Checker

**Purpose**: Verifies all dependencies are installed before running the main script.

**Usage**:
```bash
python check_requirements.py
```

### 4. `SAMPLE_CSV_FORMAT.md` - CSV Format Documentation

**Purpose**: Documents the expected CSV format and provides examples of how the script processes data.

### 5. `test_sanitization.py` - Sanitization Test Script

**Purpose**: Demonstrates the special character sanitization functionality.

**Usage**:
```bash
python test_sanitization.py
```

## Features

### Excel Logging System
- **Comprehensive Tracking**: Logs every file processing operation with detailed information
- **Excel Format**: Generates professional Excel reports with formatted columns and color-coding
- **Column Structure**:
  - **Carved Filename**: Original filename in Attachments folder (e.g., `0000001_Carved.xlsx`)
  - **Origin Filename**: Original filename extracted from CSV (e.g., `document.xlsx`)
  - **Replaced Filename**: Final filename after processing (handles duplicates)
  - **Origin Folder Path**: Original full path from CSV data
  - **Replaced Folder Path**: Actual destination folder path
  - **Error Status**: Processing status ("No error" or specific error message)
  - **Export Folder**: Which export folder was processed
  - **CSV Source**: Source CSV file name
  - **Timestamp**: When the processing occurred
- **Visual Indicators**: Green highlighting for successful operations, red for errors
- **Automatic Naming**: Log files named with timestamp (e.g., `File_Processing_Log_20241211_143022.xlsx`)

### Special Character Sanitization
- **Automatic Sanitization**: Replaces prohibited Windows filename characters with underscores
- **Prohibited Characters**: `\ / : * ? " < > | $ #`
- **Examples**:
  - `Report*Document?.docx` → `Report_Document_.docx`
  - `winre$#*.wim` → `winre___.wim`
  - `file"name".txt` → `file_name_.txt`
- **Path Safety**: Ensures all created folders and files are Windows-compatible
- **Preserves Extensions**: File extensions remain unchanged during sanitization

### Path Length Handling
- Automatically detects Windows path length limitations (>200 characters)
- Creates simplified paths using hash identifiers for extremely long paths
- Example: `Windows\LONG_PATH_883aabfb\d78` for very long original paths

### Duplicate Prevention
- Ignores duplicate source paths when creating folder structure
- Handles duplicate filenames during restoration

### Error Handling
- Graceful handling of missing files or folders
- Detailed error reporting for troubleshooting
- Continues processing even if individual operations fail

### Summary Reporting
- Shows number of CSV files processed
- Reports folders created and files moved
- Displays remaining files in Attachments folders

## Folder Structure

### Before Processing:
```
Export/
├── Attachments/
│   ├── file1.xlsx
│   └── file2.xlsx
├── Microsoft Excel Documents.csv
└── ExportSummary.json

Export (1)/
├── Attachments/
│   ├── file3.xlsx
│   └── file4.xlsx
├── Microsoft Excel Documents.csv
└── ExportSummary.json
```

### After Processing:
```
Export/
├── Attachments/ (empty)
├── Windows/
│   ├── Users/subha/Downloads/
│   │   └── file1.xlsx
│   └── LONG_PATH_883aabfb/d78/
│       └── file2.xlsx
├── Microsoft Excel Documents.csv
└── ExportSummary.json

Export (1)/
├── Attachments/ (empty)
├── Windows/
│   └── Users/subha/Downloads/
│       ├── file3.xlsx
│       └── downloads/
│           └── file4.xlsx
├── Microsoft Excel Documents.csv
└── ExportSummary.json
```

## Requirements

- Python 3.6 or higher
- Standard library modules: `os`, `csv`, `shutil`, `re`, `hashlib`, `datetime`, `dataclasses`, `typing`
- **For Excel Logging**: `openpyxl` (install with `pip install openpyxl`)

## Notes

- The scripts are designed to work with Windows-style paths
- Long path handling ensures compatibility with Windows file system limitations
- All operations are reversible using the restoration script
- **CSV files must contain these required columns**:
  - `Carved Filename`: The carved filename in Attachments folder
  - `Filename`: The original filename to rename to
  - `Source`: The original source path
- Files in Attachments folders should match the `Carved Filename` column values in CSV
- Special characters in filenames and paths are automatically sanitized

## Safety Features

- Confirmation prompts for destructive operations
- Detailed logging of all operations
- Error handling prevents data loss
- Restoration capability to undo changes

## Troubleshooting

1. **Path too long errors**: The script automatically handles these by creating simplified paths
2. **Missing files**: Check that filenames in CSV match actual files in Attachments
3. **Permission errors**: Ensure you have write permissions to the Export directories
4. **CSV format issues**: Verify CSV files have proper `File` and `Source` columns

## Example Output

```
Starting Export Folder Processing Script with Excel Logging
============================================================
✓ Excel logging initialized successfully

Processing export folder: Export
Processing CSV: Microsoft Excel Documents.csv
Found 2 entries in Microsoft Excel Documents.csv
Creating 2 unique folder paths
Created folder: Export\Windows\Users\subha\AppData\Local\Google\DriveFS\102335774010855970779\content_cache\d21\d78
Warning: Path too long, created simplified path
  Original: C:\Windows\Users\subha\Downloads\KAPE_23052024_ZIP\...
  Simplified: Windows\LONG_PATH_883aabfb\d78
Created folder: Export\Windows\LONG_PATH_883aabfb\d78
Moving files to corresponding folders...
Moved and renamed: 0000001_Carved.xlsx -> document.xlsx in Export\Windows\Users\subha\AppData\Local\Google\DriveFS\102335774010855970779\content_cache\d21\d78
Moved and renamed: 0000002_Carved.xlsx -> report.xlsx in Export\Windows\LONG_PATH_883aabfb\d78

==================================================
PROCESSING SUMMARY
==================================================

Export folder: Export
  CSV files processed: 1
  Folders created: 13
  Files remaining in Attachments: 0

✓ Excel log saved successfully: File_Processing_Log_20241211_143022.xlsx
  Total records logged: 2

============================================================
Processing completed!
Files have been organized according to their source paths.
Check the created folder structure in each Export directory.
Check the Excel log file for detailed processing information.
```
