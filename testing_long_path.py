import os

# Base path to start from (change if needed)
base_path = os.path.expanduser("~\\Desktop\\long_path_test")

# Single folder name to repeat
folder_name = "nested_folder"

# How many times to repeat the folder
repeat_count = 50  # Adjust to reach > 260 chars

# Build the full path
long_path = base_path
for i in range(repeat_count):
    long_path = os.path.join(long_path, f"{folder_name}_{i}")

try:
    os.makedirs(long_path)
    print(f"Successfully created long path:\n{long_path}")
    print(f"Total path length: {len(long_path)} characters")
except Exception as e:
    print(f"Failed to create long path: {e}")
